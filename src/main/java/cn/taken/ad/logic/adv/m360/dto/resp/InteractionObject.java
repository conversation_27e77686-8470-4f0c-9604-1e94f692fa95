package cn.taken.ad.logic.adv.m360.dto.resp;

public class InteractionObject {
    private String url; //Y（浏览或者下载类广告） 落地页地址，目前均会填充，须支持HTTP和HTTPS,可选择替换__EVENT_TIME_START__,__EVENT_TIME_END__,__OFFSET_X__，__OFFSET_Y__宏，宏替换规则参考落地页与点击监测宏替换说明。对于视频广告，需要替换__VIDEO_DURATION__宏，表示用户点击时，视频已播放时长秒数
    private String deep_link; // app deep link地址，网页类或下载类广告均有可能填充，处理优先级应高于url，大部分电商广告会填充
    private String shop_direct_ld; // 商店直投落地页地址,处理优先级低于deep_link，高于url
    private String phone; //Y（电话或者短信类广告） 目的电话号码
    private String mail; //Y（邮件类广告） 目的邮箱地址
    private String msg; //Y（短信或者邮件类广告） 内容
    private WXMiniProgInfo wxminiprog_info; // 微信小程序投放。具体请参考微信小程序opensdk官方文档：https://developers.weixin.qq.com/doc/oplatform/Mobile_App/Launching_a_Mini_Program/Launching_a_Mini_Program.html


    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getDeep_link() {
        return deep_link;
    }

    public void setDeep_link(String deep_link) {
        this.deep_link = deep_link;
    }

    public String getShop_direct_ld() {
        return shop_direct_ld;
    }

    public void setShop_direct_ld(String shop_direct_ld) {
        this.shop_direct_ld = shop_direct_ld;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public WXMiniProgInfo getWxminiprog_info() {
        return wxminiprog_info;
    }

    public void setWxminiprog_info(WXMiniProgInfo wxminiprog_info) {
        this.wxminiprog_info = wxminiprog_info;
    }
}
