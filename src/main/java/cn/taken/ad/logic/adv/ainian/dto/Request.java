package cn.taken.ad.logic.adv.ainian.dto;

import cn.taken.ad.logic.adv.ainian.dto.req.App;
import cn.taken.ad.logic.adv.ainian.dto.req.Device;
import cn.taken.ad.logic.adv.ainian.dto.req.Imp;
import cn.taken.ad.logic.adv.ainian.dto.req.User;

public class Request {
    private String id;     //	required	请求id，需保证其唯⼀性,小于64位字符
    private App app;    //required	应用详情对象,参见 App Object
    private Imp imp;        //required	Imp 对象,参见Imp Object
    private Device device;//	required	用户设备信息对象,参见 Device Object
    private User user;//optional	设备用户（广告受众）对象,参见 User Object
    private Boolean debug;//	required	默认false,true测试，false正式

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public App getApp() {
        return app;
    }

    public void setApp(App app) {
        this.app = app;
    }

    public Imp getImp() {
        return imp;
    }

    public void setImp(Imp imp) {
        this.imp = imp;
    }

    public Device getDevice() {
        return device;
    }

    public void setDevice(Device device) {
        this.device = device;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Boolean getDebug() {
        return debug;
    }

    public void setDebug(Boolean debug) {
        this.debug = debug;
    }
}
