package cn.taken.ad.logic.adv.busi.dto;

import java.io.Serializable;

/**
 * MADBOXI图片信息
 */
public class MadBoxiResponseImage implements Serializable {
    private static final long serialVersionUID = -831049895855023136L;
    
    /**
     * 图片URL
     */
    private String url;
    
    /**
     * 图片宽度
     */
    private Integer w;
    
    /**
     * 图片高度
     */
    private Integer h;

    /**
     * 图片高度
     */
    private Integer hmin;

    /**
     * 图片高度
     */
    private Integer type;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getW() {
        return w;
    }

    public void setW(Integer w) {
        this.w = w;
    }

    public Integer getH() {
        return h;
    }

    public void setH(Integer h) {
        this.h = h;
    }

    public Integer getHmin() {
        return hmin;
    }

    public void setHmin(Integer hmin) {
        this.hmin = hmin;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
