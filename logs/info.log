[2025-07-01 16:44:39.230] [cn.taken.ad.RtbApplication.logStarting,50] INFO  - Starting RtbApplication on chang with PID 10400 (D:\ideaProjects\ssc\ssp-rtb-service\target\classes started by chang in D:\ideaProjects\ssc)
[2025-07-01 16:44:39.240] [cn.taken.ad.RtbApplication.logStartupProfileInfo,652] INFO  - The following profiles are active: dev
[2025-07-01 16:44:41.042] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize,90] INFO  - Tomcat initialized with port(s): 9090 (http)
[2025-07-01 16:44:41.070] [org.apache.coyote.http11.Http11NioProtocol.log,173] INFO  - Initializing ProtocolHandler ["http-nio-9090"]
[2025-07-01 16:44:41.081] [org.apache.catalina.core.StandardService.log,173] INFO  - Starting service [Tomcat]
[2025-07-01 16:44:41.083] [org.apache.catalina.core.StandardEngine.log,173] INFO  - Starting Servlet engine: [Apache Tomcat/9.0.36]
[2025-07-01 16:44:41.208] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].log,173] INFO  - Initializing Spring embedded WebApplicationContext
[2025-07-01 16:44:41.208] [org.springframework.web.context.ContextLoader.prepareWebApplicationContext,284] INFO  - Root WebApplicationContext: initialization completed in 1920 ms
[2025-07-01 16:44:42.389] [cn.taken.ad.configuration.server.ServerInfoManager.getNew,56] INFO  - new service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:44:43.718] [org.apache.coyote.http11.Http11NioProtocol.log,173] INFO  - Starting ProtocolHandler ["http-nio-9090"]
[2025-07-01 16:44:43.863] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start,202] INFO  - Tomcat started on port(s): 9090 (http) with context path ''
[2025-07-01 16:44:43.911] [cn.taken.ad.RtbApplication.logStarted,59] INFO  - Started RtbApplication in 5.252 seconds (JVM running for 6.795)
[2025-07-01 16:44:43.923] [org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler.initialize,171] INFO  - Initializing ExecutorService
[2025-07-01 16:44:43.925] [cn.taken.ad.component.superscheduler.SuperScheduler.start,102] INFO  - super-scheduler starting
[2025-07-01 16:44:43.939] [cn.taken.ad.component.superscheduler.SuperScheduler.start,126] INFO  - super-scheduler started
[2025-07-01 16:44:43.942] [cn.taken.ad.configuration.lb.HSLoadBalanceListener.startLoadBalance,60] INFO  - not need change load balance
[2025-07-01 16:44:49.241] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].log,173] INFO  - Initializing Spring DispatcherServlet 'dispatcherServlet'
[2025-07-01 16:44:49.241] [org.springframework.web.servlet.DispatcherServlet.initServletBean,525] INFO  - Initializing Servlet 'dispatcherServlet'
[2025-07-01 16:44:49.251] [org.springframework.web.servlet.DispatcherServlet.initServletBean,547] INFO  - Completed initialization in 9 ms
[2025-07-01 16:44:50.076] [cn.taken.ad.logic.adv.busi.BuSiAdvProcessor.reqAdv,74] INFO  - Request:{"version":"3.1","id":"17513594815500002","slot_id":"8210","imp":[{"id":"17513594815500002","bidfloor":100.0,"secure":0}],"app":{"name":"句读","version":"1.1.0","bundle":"tech.caicheng.judourili"},"device":{"os":"1","os_version":"14","ua":"Mozilla/5.0 (Linux; Android 14; 2206122SC Build/UKQ1.231003.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/118.0.5993.80 Mobile Safari/537.36","ip":"*************","make":"XIAOMI","model":"2206122SC","width":1440,"height":3036,"imei":"867719069081567","imeimd5":"bae1d38d072f4d214bbd240d2896b774","oaid":"a96133ce3a4e08b4","oaid_md5":"c5d05513e8ea0ee0a3b3ad74e680059f","androidId":"213e52b37dd4abd6","androidid_md5":"fb28fc6902f655681ad8461c5d7e820c","mac":"02:00:00:00:00:00","macmd5":"0f607264fc6318a92b9e13c65db7cd3c","idfa":"","idfa_md5":"","openudid":"","connectiontype":4,"carrier":1,"ppi":480,"ssid":"","rom_version":"","hms":"","ag":"","boot_mark":"7370f11e-e1f8-431a-a20e-4e2d1007a97a","update_mark":"1736182055.678019164","paid":"c6ee80fe406ed95d9a41b5e24bd07190-5302568d903d8646e855912318369edf-b1d16263cca07c4001d8459597cd7e07","aaid":"","hardware_machine":"","startup_time":"1705591478444","mb_time":"1705591478.444164583","country_code":"CN","language":"zh","mem_total":-2147483648,"disk_total":-1073741824,"local_tz_name":"28800","hardware_model":"2206122SC","ios_os_version":"14","phone_name":"95aa3212745b12e651c880bbba2ac793","geo":{"latitude":24.696737,"longitude":108.030174},"device_filetime":"1702568592.000000000","caids":[]},"is_video":"1","tmax":1000}, Resp:{"ads":[{"native":{"assets":[{"id":0,"title":{"text":"京东-多-快-好-省"}},{"img":{"w":1080,"h":1920,"type":3,"url":"https://img-x.jd.com/1080x1920_s3.jpg"},"id":1}],"imp_urls":["http://w.madboxi.com/win?ads=s2s&ex=R9UH-Mx0jTOBwNP3273S0wYPgiA3UI0o-LVDXYrLI5hKdahAmA8bW7_5CcMgVEo6MO1265DGeh54t6HVkWJBdC3166emGVpcWftkzYgSFUhhkMeIPFjxs18nvxeucYsTTCsiaXyEcxxIkxaAEsrQNJZJnIKI09BvM9iuemwfKWZO8_u-smu9UomneJA0Ggh6NkmIDoj3MJwTv94WXap7WJzqdvJNcGNWcyyckP6Z5b4_CiuQS-v2Br2NDjzsp5sAyRkFx7Z05P3D2Ennmc8CnlkOzqfQkr7nLFuHZDfvFbsBr0bBRSId9Wsh5xiG_DRpGMdPynOqOA1riS6HW33fM-gBwiZfj62pQsKLarvFNxsycqo2N52YwcW8WBDUn9ykUkDGKbMTOejvJHs3WLDaI5xbTCrzgSjreD_oo04WK0FOJ4urJazjXtFiCNKcmw1aT3RvRyVng6scGJfNlKOVqoXb5NIlcGztFD3Jl9UhQb6pm3QaqfdpFtSBEeAsHLjLuprcws-vt9kx-sdzmFLtEezO8xdYXmylTFLcRY2efpUEUhuh8UrI1FS0Y8dxCvEtfWgdFOximnQ1dsJHyvuF8UQMz12qJCBrs8qTrglL9FVu-f20v4zds_XqBaAiT4dBKOvmd478ZVtTeuGt-ATFeKKxdPMLRnNMsjxrsSi_TlfMDxll9n_ZE4Aj95QYKQXp59HZuYSB15FUkX1IKgaDR1EKEta3q4_kiRJBdS3Ovq4pYlwgBsEFKMsHmgbZmsC-TQ37ipYLRFqKEe91VmACIXBxv28uRa8Dbw9BbuCFpcTd2HYr613KYFiOBsYYL5mMEsauwA_xT0VCidpaz92w_xZRlcvgfSoXT0rj4HZPMO0qQajPJhTeMh6dpvqCnf4hL0NuC9bg_H_WQ6fJ82wX_qfxSYBdf_A7TFG18sDMK2jX68z04DdgvWq9H0zZXC4HOtNyLZYz2_B-mDOho7GjRrnmo86LzlLmEWxu5B47rxhWL9GrsCrBlo_JTVzc17iQy8PKUTUDo65R-se84KVjI9vALnX3NBzcPQacdgJ9-iP_llxrJs8kSz7FgY8L79_B75av5_h-Xw-14X2UjStk4g&price=__PRICE__","http://v.madboxi.com/imp?ex=CIPAjsMGEFsYkkAiETE3NTEzNTk0ODE1NTAwMDAyKiA4OGQ2YjllMDdjYjM0MzcxYTBmM2M1NzAwYmRlNTNlNDAAOABAAEj6AVoNNjAuMjE5LjIzNi45M2ACcAB4AJgBj06wAQK4AQDCAQExyAHU16qnBNIBAzEwNdoBCG15cG9zaWQx4gEBNOoBDzg2NzcxOTA2OTA4MTU2N_IBAIICAJACAJgCALACALgCyAHaAhBhOTYxMzNjZTNhNGUwOGI0-gIgYmFlMWQzOGQwNzJmNGQyMTRiYmQyNDBkMjg5NmI3NzSCAwRwcm9kigMFZmFsc2WSAwIxNLIDBWZhbHNlugMXdGVjaC5jYWljaGVuZy5qdWRvdXJpbGnAA_AB0gMgYzVkMDU1MTNlOGVhMGVlMGEzYjNhZDc0ZTY4MDA1OWY&adx_code=s2s&slot_id=8210&material_url=aHR0cHM6Ly9pbWcteC5qZC5jb20vMTA4MHgxOTIwX3MzLmpwZw&material_id=&material_type=1"],"wx_mini_user":"gh_6617da09780d","app_package":"com.jingdong.app.mall","click_urls":["http://v.madboxi.com/clk?ex=CIPAjsMGEFsYkkAiETE3NTEzNTk0ODE1NTAwMDAyKiA4OGQ2YjllMDdjYjM0MzcxYTBmM2M1NzAwYmRlNTNlNDAAOABAAEj6AVoNNjAuMjE5LjIzNi45M2ACcAB4AJgBj06wAQK4AQDCAQExyAHU16qnBNIBAzEwNdoBCG15cG9zaWQx4gEBNOoBDzg2NzcxOTA2OTA4MTU2N_IBAIICAJACAJgCALACALgCyAHaAhBhOTYxMzNjZTNhNGUwOGI0-gIgYmFlMWQzOGQwNzJmNGQyMTRiYmQyNDBkMjg5NmI3NzSCAwRwcm9kigMFZmFsc2WSAwIxNLIDBWZhbHNlugMXdGVjaC5jYWljaGVuZy5qdWRvdXJpbGnAA_AB0gMgYzVkMDU1MTNlOGVhMGVlMGEzYjNhZDc0ZTY4MDA1OWY&adx_code=s2s&slot_id=8210&material_url=aHR0cHM6Ly9pbWcteC5qZC5jb20vMTA4MHgxOTIwX3MzLmpwZw&material_id=&material_type=1"],"deeplink_url":"openapp.jdmobile://virtual?params=%7B%22category%22%3A%22jump%22%2C%22des%22%3A%22union%22%2C%22url%22%3A%22https%3A%2F%2Fu.jd.com%2FIOCavZy%3Fe%3DCPS-11419-2004033794093276258%22%7D","dpsucc_urls":["http://v.madboxi.com/deepLink?ex=CIPAjsMGEFsYkkAiETE3NTEzNTk0ODE1NTAwMDAyKiA4OGQ2YjllMDdjYjM0MzcxYTBmM2M1NzAwYmRlNTNlNDAAOABAAEj6AVoNNjAuMjE5LjIzNi45M2ACcAB4AJgBj06wAQK4AQDCAQExyAHU16qnBNIBAzEwNdoBCG15cG9zaWQx4gEBNOoBDzg2NzcxOTA2OTA4MTU2N_IBAIICAJACAJgCALACALgCyAHaAhBhOTYxMzNjZTNhNGUwOGI0-gIgYmFlMWQzOGQwNzJmNGQyMTRiYmQyNDBkMjg5NmI3NzSCAwRwcm9kigMFZmFsc2WSAwIxNLIDBWZhbHNlugMXdGVjaC5jYWljaGVuZy5qdWRvdXJpbGnAA_AB0gMgYzVkMDU1MTNlOGVhMGVlMGEzYjNhZDc0ZTY4MDA1OWY&adx_code=s2s&slot_id=8210&material_url=aHR0cHM6Ly9pbWcteC5qZC5jb20vMTA4MHgxOTIwX3MzLmpwZw&material_id=&material_type=1&type=103"],"url":"https://ccc-x.jd.com/dsp/nc","wx_mini_path":"pages/index"},"interact_type":3,"price":240,"id":"25ee370e60404a7db64081a8dfa880d4"}],"code":0,"id":"17513594815500002"} 
[2025-07-01 16:45:03.559] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-01 16:45:03.560] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-01 16:45:03.560] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-01 16:45:03.559] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-01 16:45:03.560] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-01 16:45:13.795] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:45:43.871] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:46:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-01 16:46:01.003] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-01 16:46:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-01 16:46:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-01 16:46:01.118] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{"35-129-1-1-51-49-49-76":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202507011645","strategyId":35,"strategyTagAdvId":129,"mediaId":1,"mediaAppId":1,"mediaTagId":51,"mediaReqTotal":1,"mediaReqInvalidTotal":0,"mediaRespFailTotal":1,"mediaParticipatingTotal":1,"mediaWinTotal":0,"mediaAmount":0,"mediaMaxTime":15559,"mediaAvgTime":15559,"mediaMinTime":0,"mediaUseTimeTotal":15559,"advertiserId":49,"advertiserAppId":49,"advertiserTagId":76,"advertiserReqTotal":1,"advertiserReqSuccessTotal":1,"advertiserReqFailTotal":0,"advertiserRespFailTotal":0,"advertiserReqTimeoutTotal":0,"advertiserParticipatingTotal":1,"advertiserWinTotal":0,"advertiserAmount":0,"advertiserMaxTime":13778,"advertiserAvgTime":13778,"advertiserMinTime":0,"advertiserUseTimeTotal":13778}},minuteMediaReq:{"35-1-1-51":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202507011645","mediaId":1,"mediaAppId":1,"mediaTagId":51,"reqTotal":1,"reqInvalidTotal":0,"respFailTotal":1,"participatingTotal":1,"winTotal":0,"amount":0,"strategyId":35,"maxTime":15559,"avgTime":15559,"minTime":0,"useTimeTotal":15559}},minuteAdvReq:{"49-49-76":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202507011645","advertiserId":49,"advertiserAppId":49,"advertiserTagId":76,"reqTotal":1,"reqSuccessTotal":1,"reqFailTotal":0,"respFailTotal":0,"reqTimeoutTotal":0,"participatingTotal":1,"winTotal":0,"amount":0,"maxTime":13778,"avgTime":13778,"minTime":0,"useTimeTotal":13778}}
[2025-07-01 16:46:13.952] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:46:44.025] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:47:01.001] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-01 16:47:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-01 16:47:01.001] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-01 16:47:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-01 16:47:01.003] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-01 16:47:14.101] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:47:44.178] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:48:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-01 16:48:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-01 16:48:01.003] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-01 16:48:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-01 16:48:01.004] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-01 16:48:14.260] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:48:44.341] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:49:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-01 16:49:01.009] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-01 16:49:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-01 16:49:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-01 16:49:01.010] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-01 16:49:14.415] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:49:44.490] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:50:01.011] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-01 16:50:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-01 16:50:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-01 16:50:01.013] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-01 16:50:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-01 16:50:14.567] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:50:44.652] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:51:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-01 16:51:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-01 16:51:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-01 16:51:01.009] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-01 16:51:01.010] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-01 16:51:14.736] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:51:44.814] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:52:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-01 16:52:01.004] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-01 16:52:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-01 16:52:01.004] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-01 16:52:01.005] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-01 16:52:14.899] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:52:44.988] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:53:01.016] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-01 16:53:01.016] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-01 16:53:01.017] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-01 16:53:01.017] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-01 16:53:01.016] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-01 16:53:15.074] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:53:45.154] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:54:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-01 16:54:01.006] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-01 16:54:01.006] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-01 16:54:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-01 16:54:01.007] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-01 16:54:15.236] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:54:45.321] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:55:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-01 16:55:01.005] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-01 16:55:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-01 16:55:01.005] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-01 16:55:01.005] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-01 16:55:15.397] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:55:45.471] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:56:01.013] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-01 16:56:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-01 16:56:01.013] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-01 16:56:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-01 16:56:01.014] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-01 16:56:15.553] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:56:45.634] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:57:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-01 16:57:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-01 16:57:01.008] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-01 16:57:01.010] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-01 16:57:01.010] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-01 16:57:15.712] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:57:45.810] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:58:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-01 16:58:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-01 16:58:01.010] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-01 16:58:01.010] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-01 16:58:01.011] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-01 16:58:15.884] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:58:45.968] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:59:01.008] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-01 16:59:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-01 16:59:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-01 16:59:01.009] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-01 16:59:01.010] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-01 16:59:16.049] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 16:59:46.129] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 17:00:01.004] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-01 17:00:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-01 17:00:01.004] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-01 17:00:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-01 17:00:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-01 17:00:16.214] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 17:00:46.291] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 17:01:00.004] [cn.taken.ad.task.DmpPackageSaveTask.uploadFile,66] INFO  - start upload chunk file
[2025-07-01 17:01:00.006] [cn.taken.ad.task.DmpPackageSaveTask.uploadFile,103] INFO  - finished upload chunk file
[2025-07-01 17:01:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-01 17:01:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-01 17:01:01.005] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-01 17:01:01.006] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-01 17:01:01.007] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-01 17:01:16.366] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 17:01:46.448] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 17:02:01.007] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-07-01 17:02:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-07-01 17:02:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-07-01 17:02:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-07-01 17:02:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-07-01 17:02:16.523] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
[2025-07-01 17:02:38.776] [cn.taken.ad.configuration.lb.HSLoadBalanceListener.stop,94] INFO  - not need change load balance
[2025-07-01 17:02:38.776] [cn.taken.ad.component.superscheduler.SuperScheduler.stop,130] INFO  - super-scheduler stopping
[2025-07-01 17:02:38.777] [org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler.shutdown,208] INFO  - Shutting down ExecutorService
[2025-07-01 17:02:38.783] [cn.taken.ad.component.superscheduler.SuperScheduler.stop,140] INFO  - super-scheduler stopped
[2025-07-01 17:02:38.847] [cn.taken.ad.configuration.server.ServerInfoManager.unsubscribe,87] INFO  - unsubscribe service server RTB , 65b8aa0a50b24225bf646e6f1ec3068e , 02
