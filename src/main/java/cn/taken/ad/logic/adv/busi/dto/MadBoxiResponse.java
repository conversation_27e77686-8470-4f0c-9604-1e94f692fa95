package cn.taken.ad.logic.adv.busi.dto;

import java.io.Serializable;
import java.util.List;

/**
 * MADBOXI响应主体
 */
public class MadBoxiResponse implements Serializable {
    private static final long serialVersionUID = -831049895855023133L;
    
    /**
     * 请求 ID
     */
    private String id;
    
    /**
     * 响应码，见附录-响应码
     */
    private Integer code;
    
    /**
     * 响应描述信息
     */
    private String msg;

    /**
     * 广告列表
     */
    private List<MadBoxiResponseAd> ads;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<MadBoxiResponseAd> getAds() {
        return ads;
    }

    public void setAds(List<MadBoxiResponseAd> ads) {
        this.ads = ads;
    }
}
