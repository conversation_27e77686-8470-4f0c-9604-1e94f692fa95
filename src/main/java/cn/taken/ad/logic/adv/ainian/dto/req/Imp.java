package cn.taken.ad.logic.adv.ainian.dto.req;

public class Imp {

    private String id; //曝光标识 ID，通常以 1 开头并递增。
    private String tagId; //广告位 ID，需要从平台申请(由商务分配)
    private Integer w; //广告位宽
    private Integer h; //广告位高
    private Integer pos; //广告展现位置：1=开屏，2=横屏，3=插屏，4=信息流，5=视频，6=激励视频
    private Boolean isBid; //竞价：false 不参与（默认）；true 参与
    private Integer bidFloor; //cpm底价，单位为分
    private String dealid; //私有化交易ID(由媒体方提供)
    private Integer mind; //期望视频的最⼩⻓度（秒）
    private Integer maxd; //期望视频的最大⻓度（秒）

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTagId() {
        return tagId;
    }

    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    public Integer getW() {
        return w;
    }

    public void setW(Integer w) {
        this.w = w;
    }

    public Integer getH() {
        return h;
    }

    public void setH(Integer h) {
        this.h = h;
    }

    public Integer getPos() {
        return pos;
    }

    public void setPos(Integer pos) {
        this.pos = pos;
    }

    public Boolean getBid() {
        return isBid;
    }

    public void setBid(Boolean bid) {
        isBid = bid;
    }

    public Integer getBidFloor() {
        return bidFloor;
    }

    public void setBidFloor(Integer bidFloor) {
        this.bidFloor = bidFloor;
    }

    public String getDealid() {
        return dealid;
    }

    public void setDealid(String dealid) {
        this.dealid = dealid;
    }

    public Integer getMind() {
        return mind;
    }

    public void setMind(Integer mind) {
        this.mind = mind;
    }

    public Integer getMaxd() {
        return maxd;
    }

    public void setMaxd(Integer maxd) {
        this.maxd = maxd;
    }
}
