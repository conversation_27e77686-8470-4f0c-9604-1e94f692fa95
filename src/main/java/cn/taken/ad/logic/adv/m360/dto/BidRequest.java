package cn.taken.ad.logic.adv.m360.dto;

import cn.taken.ad.logic.adv.m360.dto.req.Adspace;
import cn.taken.ad.logic.adv.m360.dto.req.App;
import cn.taken.ad.logic.adv.m360.dto.req.Device;

import java.util.List;

public class BidRequest {

    private String bid; //Y 请求的唯一id，32个字符的小写字符串，由媒体端生成，需要保持唯一性。生成规则：任意设备号（比如IMEI）+APP包名+毫秒时间戳，MD5后取小写值
    private Integer req_times; // req_times用于标识同一个信息流广告位在当前频道下，信息流从上拉、从下拉发起广告请求的次数。信息流广告位的第一次广告请求编号为1；若之后信息流从下拉（即用户手指上滑、从下方加载数据）过程中，触发该广告位的广告请求，则从2开始编号，不断递加；若之后信息流从上拉（即用户手指下滑、从上方加载数据）过程中，触发该广告位的广告请求，则从-2开始编号，不断递减。无符号代表用户手指上滑，从下方加载数据；有负号代表代表用户手指下滑，从上方加载数据，两种行为分别计数
    private App app; //Y App对象，客户端APP的信息，必须真实来源于客户端
    private Device device; //Y Device对象，用户的设备信息，必须真实来源于客户端
    private List<Adspace> adspaces; //Y Adspace对象列表，广告位信息，必须真实来源于客户端，目前仅支持单个广告位<br>
    private String uid; //Y（当且仅当一个页面有多个广告请求时） 标识用户浏览某个页面的行为，避免用户在同一页面不同位置上较大概率地看到相同的广告。32个字符的小写字符串，由媒体端生成，需要与广告位所在的页面属性相关，且不能与bid字段相同。生成规则：任意设备号（比如IMEI）+APP包名+媒体自定义页面属性（比如Activity/Fragment的hashCode，或者类似电影、动漫等），MD5后取小写值
    private String ip; //Y（当且仅当使用服务器发送广告请求时） 客户端真实的外网IP地址，不能是代理服务器IP（比如透明代理，小区或城域网出口），不能是内网IP
    private String ipv6; // 客户端真实的外网ipv6地址
    private String user_agent; //Y 客户端的UserAgent。必须是客户端通过系统API获取的真实UA，不能自定义
    private String utag; // 用户的兴趣标签，多个兴趣标签使用英文逗号分隔（不能有空格）
    private Integer network_type; //Y 网络类型{<br> NET_UNKNOWN = 0; //未知<br> NET_WIFI = 1; //wifi <br> NET_2G =2 ; //2G <br> NET_3G =3 ; //3G <br> NET_4G = 4; //4G<br>NET_5G = <font color="red"><bold>6</bold></font>; //5G<br>}<br>必须区分WiFi和移动网络，如果移动网络下不能区分2345G，请统一填写2G，unknown只能用完全无法识别的情况下
    private Double longitude; // 地理位置，经度
    private Double latitude; // 地理位置，纬度
    private String ext; // 请求协议中没有列出的字段，以K-V对的形式给出
    private String search_word; // 用户的搜索关键词(对于换包场景,此字段应该填写换包的包名）
    private String installed_pkgs; // 用户的已安装的APP列表(最多可以上报TOP 20，多个包名之间用英文逗号分隔)，获取用户已安装APP列表TOP N，排除系统应用，参考以下两种APP优先级计算方式：方式一： 按照APP安装时间，取最近安装的N个上报；方式二： 按照APP安装率，该媒体的所有用户中，某APP的安装率越高，则该APP的优先级越高，取优先级最高的N个APP上报；为了更好的广告效果以及收益，建议使用方式二上报安装列表。
    private Integer wxminiprog; // 设备是否支持调起小程序,0:不支持微信小程序 1:支持微信小程序.<br> 支持：设备安装了微信，且当前app集成了微信opensdk，等等。具体请参考微信小程序opensdk官方文档：https://developers.weixin.qq.com/doc/oplatform/Mobile_App/Launching_a_Mini_Program/Launching_a_Mini_Program.html
    private Integer crec; // 1:本次请求关闭个性化推荐、其他情况(除1之外任意情况):启用个性化推荐。注意仅对本次请求生效，如果要持续关闭个性化推荐功能，则需要每次请求都有crec参数。


    public String getBid() {
        return bid;
    }

    public void setBid(String bid) {
        this.bid = bid;
    }

    public Integer getReq_times() {
        return req_times;
    }

    public void setReq_times(Integer req_times) {
        this.req_times = req_times;
    }

    public App getApp() {
        return app;
    }

    public void setApp(App app) {
        this.app = app;
    }

    public Device getDevice() {
        return device;
    }

    public void setDevice(Device device) {
        this.device = device;
    }

    public List<Adspace> getAdspaces() {
        return adspaces;
    }

    public void setAdspaces(List<Adspace> adspaces) {
        this.adspaces = adspaces;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getIpv6() {
        return ipv6;
    }

    public void setIpv6(String ipv6) {
        this.ipv6 = ipv6;
    }

    public String getUser_agent() {
        return user_agent;
    }

    public void setUser_agent(String user_agent) {
        this.user_agent = user_agent;
    }

    public String getUtag() {
        return utag;
    }

    public void setUtag(String utag) {
        this.utag = utag;
    }

    public Integer getNetwork_type() {
        return network_type;
    }

    public void setNetwork_type(Integer network_type) {
        this.network_type = network_type;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }

    public String getSearch_word() {
        return search_word;
    }

    public void setSearch_word(String search_word) {
        this.search_word = search_word;
    }

    public String getInstalled_pkgs() {
        return installed_pkgs;
    }

    public void setInstalled_pkgs(String installed_pkgs) {
        this.installed_pkgs = installed_pkgs;
    }

    public Integer getWxminiprog() {
        return wxminiprog;
    }

    public void setWxminiprog(Integer wxminiprog) {
        this.wxminiprog = wxminiprog;
    }

    public Integer getCrec() {
        return crec;
    }

    public void setCrec(Integer crec) {
        this.crec = crec;
    }
}
