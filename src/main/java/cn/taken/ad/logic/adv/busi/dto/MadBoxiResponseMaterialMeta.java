package cn.taken.ad.logic.adv.busi.dto;

import java.io.Serializable;
import java.util.List;

/**
 * MADBOXI创意素材信息
 */
public class MadBoxiResponseMaterialMeta implements Serializable {
    private static final long serialVersionUID = -831049895855023135L;
    
    /**
     * 广告标题
     */
    private String title;
    
    /**
     * 广告描述
     */
    private String description;
    
    /**
     * 创意类型 1:文本 2:图文 3:大图 4:视频 5:HTML
     */
    private Integer creativeType;
    
    /**
     * 图片模式 2:小图 3:大图 4:多图 5:横屏视频 15:竖屏视频
     */
    private Integer imageMode;
    
    /**
     * 交互类型 2:系统浏览器 3:内置浏览器 4:下载 5:深度链接 6:小程序
     */
    private Integer interactionType;
    
    /**
     * 目标链接
     */
    private String targetUrl;
    
    /**
     * 下载链接
     */
    private String downloadUrl;
    
    /**
     * 深度链接
     */
    private String deeplink;
    
    /**
     * Universal Link
     */
    private String universalLink;
    
    /**
     * 微信小程序AppId
     */
    private String wechatAppId;
    
    /**
     * 微信小程序路径
     */
    private String wechatAppPath;
    
    /**
     * 图标
     */
    private String icon;
    
    /**
     * Logo信息
     */
    private MadBoxiResponseImage logo;
    
    /**
     * 广告Logo
     */
    private MadBoxiResponseImage adLogo;
    
    /**
     * 广告文本
     */
    private MadBoxiResponseImage adText;
    
    /**
     * 主图信息
     */
    private MadBoxiResponseImage image;
    
    /**
     * 图片列表
     */
    private List<MadBoxiResponseImage> imageList;
    
    /**
     * 视频信息
     */
    private MadBoxiResponseVideo video;
    
    /**
     * 激励视频信息
     */
    private MadBoxiResponseRewardInfo adRewardInfo;
    
    /**
     * HTML片段
     */
    private String htmlSnippet;
    
    /**
     * HTML宽度
     */
    private Integer htmlWidth;
    
    /**
     * HTML高度
     */
    private Integer htmlHeight;
    
    /**
     * 应用名称
     */
    private String appName;
    
    /**
     * 应用包名
     */
    private String appPackage;
    
    /**
     * 应用图标
     */
    private String appIconUrl;
    
    /**
     * 应用大小（MB）
     */
    private String appSize;
    
    /**
     * 应用开发者
     */
    private String appDeveloper;
    
    /**
     * 应用版本
     */
    private String appVersionName;
    
    /**
     * 应用描述
     */
    private String appDescription;
    
    /**
     * 应用权限信息链接
     */
    private String appPermissionUrl;
    
    /**
     * 应用隐私政策链接
     */
    private String appPrivacyPolicyUrl;
    
    /**
     * 展现监测链接
     */
    private List<String> showUrl;
    
    /**
     * 点击监测链接
     */
    private List<String> clickUrl;
    
    /**
     * 关闭监测链接
     */
    private List<String> closeUrl;
    
    /**
     * 下载开始监测链接
     */
    private List<String> dlStartUrl;
    
    /**
     * 下载完成监测链接
     */
    private List<String> dlFinishUrl;
    
    /**
     * 安装开始监测链接
     */
    private List<String> instStartUrl;
    
    /**
     * 安装完成监测链接
     */
    private List<String> instFinishUrl;
    
    /**
     * 安装后打开监测链接
     */
    private List<String> instOpenUrl;
    
    /**
     * 激活监测链接
     */
    private List<String> instActiveUrl;
    
    /**
     * 深度链接打开监测链接
     */
    private List<String> deeplinkOpenUrl;
    
    /**
     * 深度链接成功监测链接
     */
    private List<String> deeplinkSucUrl;
    
    /**
     * 深度链接失败监测链接
     */
    private List<String> deeplinkFailUrl;
    
    /**
     * 视频加载监测链接
     */
    private List<String> videoLoadUrl;
    
    /**
     * 视频跳过监测链接
     */
    private List<String> videoSkippedUrl;
    
    /**
     * 视频关闭监测链接
     */
    private List<String> videoClosedUrl;
    
    /**
     * 视频开始播放监测链接
     */
    private List<String> playStartUrl;
    
    /**
     * 视频播放完成监测链接
     */
    private List<String> playCompleteUrl;
    
    /**
     * 全屏播放监测链接
     */
    private List<String> fullScreenPlayUrl;
    
    /**
     * 竞胜通知链接
     */
    private List<String> winNoticeUrl;
    
    /**
     * 竞败通知链接
     */
    private List<String> lossNoticeUrl;

    // Getters and Setters
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getCreativeType() {
        return creativeType;
    }

    public void setCreativeType(Integer creativeType) {
        this.creativeType = creativeType;
    }

    public Integer getImageMode() {
        return imageMode;
    }

    public void setImageMode(Integer imageMode) {
        this.imageMode = imageMode;
    }

    public Integer getInteractionType() {
        return interactionType;
    }

    public void setInteractionType(Integer interactionType) {
        this.interactionType = interactionType;
    }

    public String getTargetUrl() {
        return targetUrl;
    }

    public void setTargetUrl(String targetUrl) {
        this.targetUrl = targetUrl;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public String getDeeplink() {
        return deeplink;
    }

    public void setDeeplink(String deeplink) {
        this.deeplink = deeplink;
    }

    public String getUniversalLink() {
        return universalLink;
    }

    public void setUniversalLink(String universalLink) {
        this.universalLink = universalLink;
    }

    public String getWechatAppId() {
        return wechatAppId;
    }

    public void setWechatAppId(String wechatAppId) {
        this.wechatAppId = wechatAppId;
    }

    public String getWechatAppPath() {
        return wechatAppPath;
    }

    public void setWechatAppPath(String wechatAppPath) {
        this.wechatAppPath = wechatAppPath;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public MadBoxiResponseImage getLogo() {
        return logo;
    }

    public void setLogo(MadBoxiResponseImage logo) {
        this.logo = logo;
    }

    public MadBoxiResponseImage getAdLogo() {
        return adLogo;
    }

    public void setAdLogo(MadBoxiResponseImage adLogo) {
        this.adLogo = adLogo;
    }

    public MadBoxiResponseImage getAdText() {
        return adText;
    }

    public void setAdText(MadBoxiResponseImage adText) {
        this.adText = adText;
    }

    public MadBoxiResponseImage getImage() {
        return image;
    }

    public void setImage(MadBoxiResponseImage image) {
        this.image = image;
    }

    public List<MadBoxiResponseImage> getImageList() {
        return imageList;
    }

    public void setImageList(List<MadBoxiResponseImage> imageList) {
        this.imageList = imageList;
    }

    public MadBoxiResponseVideo getVideo() {
        return video;
    }

    public void setVideo(MadBoxiResponseVideo video) {
        this.video = video;
    }

    public MadBoxiResponseRewardInfo getAdRewardInfo() {
        return adRewardInfo;
    }

    public void setAdRewardInfo(MadBoxiResponseRewardInfo adRewardInfo) {
        this.adRewardInfo = adRewardInfo;
    }

    public String getHtmlSnippet() {
        return htmlSnippet;
    }

    public void setHtmlSnippet(String htmlSnippet) {
        this.htmlSnippet = htmlSnippet;
    }

    public Integer getHtmlWidth() {
        return htmlWidth;
    }

    public void setHtmlWidth(Integer htmlWidth) {
        this.htmlWidth = htmlWidth;
    }

    public Integer getHtmlHeight() {
        return htmlHeight;
    }

    public void setHtmlHeight(Integer htmlHeight) {
        this.htmlHeight = htmlHeight;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppPackage() {
        return appPackage;
    }

    public void setAppPackage(String appPackage) {
        this.appPackage = appPackage;
    }

    public String getAppIconUrl() {
        return appIconUrl;
    }

    public void setAppIconUrl(String appIconUrl) {
        this.appIconUrl = appIconUrl;
    }

    public String getAppSize() {
        return appSize;
    }

    public void setAppSize(String appSize) {
        this.appSize = appSize;
    }

    public String getAppDeveloper() {
        return appDeveloper;
    }

    public void setAppDeveloper(String appDeveloper) {
        this.appDeveloper = appDeveloper;
    }

    public String getAppVersionName() {
        return appVersionName;
    }

    public void setAppVersionName(String appVersionName) {
        this.appVersionName = appVersionName;
    }

    public String getAppDescription() {
        return appDescription;
    }

    public void setAppDescription(String appDescription) {
        this.appDescription = appDescription;
    }

    public String getAppPermissionUrl() {
        return appPermissionUrl;
    }

    public void setAppPermissionUrl(String appPermissionUrl) {
        this.appPermissionUrl = appPermissionUrl;
    }

    public String getAppPrivacyPolicyUrl() {
        return appPrivacyPolicyUrl;
    }

    public void setAppPrivacyPolicyUrl(String appPrivacyPolicyUrl) {
        this.appPrivacyPolicyUrl = appPrivacyPolicyUrl;
    }

    public List<String> getShowUrl() {
        return showUrl;
    }

    public void setShowUrl(List<String> showUrl) {
        this.showUrl = showUrl;
    }

    public List<String> getClickUrl() {
        return clickUrl;
    }

    public void setClickUrl(List<String> clickUrl) {
        this.clickUrl = clickUrl;
    }

    public List<String> getCloseUrl() {
        return closeUrl;
    }

    public void setCloseUrl(List<String> closeUrl) {
        this.closeUrl = closeUrl;
    }

    public List<String> getDlStartUrl() {
        return dlStartUrl;
    }

    public void setDlStartUrl(List<String> dlStartUrl) {
        this.dlStartUrl = dlStartUrl;
    }

    public List<String> getDlFinishUrl() {
        return dlFinishUrl;
    }

    public void setDlFinishUrl(List<String> dlFinishUrl) {
        this.dlFinishUrl = dlFinishUrl;
    }

    public List<String> getInstStartUrl() {
        return instStartUrl;
    }

    public void setInstStartUrl(List<String> instStartUrl) {
        this.instStartUrl = instStartUrl;
    }

    public List<String> getInstFinishUrl() {
        return instFinishUrl;
    }

    public void setInstFinishUrl(List<String> instFinishUrl) {
        this.instFinishUrl = instFinishUrl;
    }

    public List<String> getInstOpenUrl() {
        return instOpenUrl;
    }

    public void setInstOpenUrl(List<String> instOpenUrl) {
        this.instOpenUrl = instOpenUrl;
    }

    public List<String> getInstActiveUrl() {
        return instActiveUrl;
    }

    public void setInstActiveUrl(List<String> instActiveUrl) {
        this.instActiveUrl = instActiveUrl;
    }

    public List<String> getDeeplinkOpenUrl() {
        return deeplinkOpenUrl;
    }

    public void setDeeplinkOpenUrl(List<String> deeplinkOpenUrl) {
        this.deeplinkOpenUrl = deeplinkOpenUrl;
    }

    public List<String> getDeeplinkSucUrl() {
        return deeplinkSucUrl;
    }

    public void setDeeplinkSucUrl(List<String> deeplinkSucUrl) {
        this.deeplinkSucUrl = deeplinkSucUrl;
    }

    public List<String> getDeeplinkFailUrl() {
        return deeplinkFailUrl;
    }

    public void setDeeplinkFailUrl(List<String> deeplinkFailUrl) {
        this.deeplinkFailUrl = deeplinkFailUrl;
    }

    public List<String> getVideoLoadUrl() {
        return videoLoadUrl;
    }

    public void setVideoLoadUrl(List<String> videoLoadUrl) {
        this.videoLoadUrl = videoLoadUrl;
    }

    public List<String> getVideoSkippedUrl() {
        return videoSkippedUrl;
    }

    public void setVideoSkippedUrl(List<String> videoSkippedUrl) {
        this.videoSkippedUrl = videoSkippedUrl;
    }

    public List<String> getVideoClosedUrl() {
        return videoClosedUrl;
    }

    public void setVideoClosedUrl(List<String> videoClosedUrl) {
        this.videoClosedUrl = videoClosedUrl;
    }

    public List<String> getPlayStartUrl() {
        return playStartUrl;
    }

    public void setPlayStartUrl(List<String> playStartUrl) {
        this.playStartUrl = playStartUrl;
    }

    public List<String> getPlayCompleteUrl() {
        return playCompleteUrl;
    }

    public void setPlayCompleteUrl(List<String> playCompleteUrl) {
        this.playCompleteUrl = playCompleteUrl;
    }

    public List<String> getFullScreenPlayUrl() {
        return fullScreenPlayUrl;
    }

    public void setFullScreenPlayUrl(List<String> fullScreenPlayUrl) {
        this.fullScreenPlayUrl = fullScreenPlayUrl;
    }

    public List<String> getWinNoticeUrl() {
        return winNoticeUrl;
    }

    public void setWinNoticeUrl(List<String> winNoticeUrl) {
        this.winNoticeUrl = winNoticeUrl;
    }

    public List<String> getLossNoticeUrl() {
        return lossNoticeUrl;
    }

    public void setLossNoticeUrl(List<String> lossNoticeUrl) {
        this.lossNoticeUrl = lossNoticeUrl;
    }
}
