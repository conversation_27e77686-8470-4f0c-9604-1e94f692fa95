package cn.taken.ad.logic.adv.busi.dto;

import java.io.Serializable;
import java.util.List;

/**
 * MADBOXI广告位信息
 */
public class MadBoxiRequestAdSlot implements Serializable {
    private static final long serialVersionUID = -7064608428216363192L;
    
    /**
     * 广告位ID
     */
    private String slotId;
    
    /**
     * 广告类型 1:横幅 2:插屏 3:开屏 5:信息流 7:激励视频 9:原生
     */
    private Integer adType;
    
    /**
     * 广告位尺寸列表
     */
    private List<MadBoxiRequestSlotSize> slotSizes;
    
    /**
     * 广告数量
     */
    private Integer adCount;
    
    /**
     * 最小时长（秒）
     */
    private Integer minDuration;
    
    /**
     * 最大时长（秒）
     */
    private Integer maxDuration;
    
    /**
     * 视频格式 0:任意格式 1:flv 2:mp4
     */
    private Integer videoType;
    
    /**
     * 出价（分）
     */
    private Integer bidPrice;

    public String getSlotId() {
        return slotId;
    }

    public void setSlotId(String slotId) {
        this.slotId = slotId;
    }

    public Integer getAdType() {
        return adType;
    }

    public void setAdType(Integer adType) {
        this.adType = adType;
    }

    public List<MadBoxiRequestSlotSize> getSlotSizes() {
        return slotSizes;
    }

    public void setSlotSizes(List<MadBoxiRequestSlotSize> slotSizes) {
        this.slotSizes = slotSizes;
    }

    public Integer getAdCount() {
        return adCount;
    }

    public void setAdCount(Integer adCount) {
        this.adCount = adCount;
    }

    public Integer getMinDuration() {
        return minDuration;
    }

    public void setMinDuration(Integer minDuration) {
        this.minDuration = minDuration;
    }

    public Integer getMaxDuration() {
        return maxDuration;
    }

    public void setMaxDuration(Integer maxDuration) {
        this.maxDuration = maxDuration;
    }

    public Integer getVideoType() {
        return videoType;
    }

    public void setVideoType(Integer videoType) {
        this.videoType = videoType;
    }

    public Integer getBidPrice() {
        return bidPrice;
    }

    public void setBidPrice(Integer bidPrice) {
        this.bidPrice = bidPrice;
    }
}
