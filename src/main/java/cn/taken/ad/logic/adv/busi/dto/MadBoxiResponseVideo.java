package cn.taken.ad.logic.adv.busi.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * MADBOXI视频信息
 */
public class MadBoxiResponseVideo implements Serializable {
    private static final long serialVersionUID = -831049895855023137L;
    
    /**
     * 视频URL
     */
    private String video_url;
    
    /**
     * 视频时长（秒）
     */
    private BigDecimal videoDuration;
    
    /**
     * 视频大小（字节）
     */
    private BigDecimal size;
    
    /**
     * 视频分辨率
     */
    private String resolution;
    
    /**
     * 封面图URL
     */
    private String coverUrl;
    
    /**
     * 封面图宽度
     */
    private Integer coverWidth;
    
    /**
     * 封面图高度
     */
    private Integer coverHeight;

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public BigDecimal getVideoDuration() {
        return videoDuration;
    }

    public void setVideoDuration(BigDecimal videoDuration) {
        this.videoDuration = videoDuration;
    }

    public BigDecimal getSize() {
        return size;
    }

    public void setSize(BigDecimal size) {
        this.size = size;
    }

    public String getResolution() {
        return resolution;
    }

    public void setResolution(String resolution) {
        this.resolution = resolution;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    public Integer getCoverWidth() {
        return coverWidth;
    }

    public void setCoverWidth(Integer coverWidth) {
        this.coverWidth = coverWidth;
    }

    public Integer getCoverHeight() {
        return coverHeight;
    }

    public void setCoverHeight(Integer coverHeight) {
        this.coverHeight = coverHeight;
    }
}
