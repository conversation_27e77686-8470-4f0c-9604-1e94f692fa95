package cn.taken.ad.logic.adv.busi.dto;

import java.io.Serializable;
import java.util.List;

/**
 * MADBOXI广告位信息
 */
public class MadBoxiRequestImp implements Serializable {
    private static final long serialVersionUID = -7064608428216363192L;
    
    /**
     * 广告位ID
     */
    private String id;
    
    /**
     * 请求底价(RTB 必填 单位为分)
     */
    private Double bidfloor;
    
    /**
     * 是否需要 https 链接，1 为需要，0 为不
     * 需要，默认为 0
     */
    private Integer secure;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Double getBidfloor() {
        return bidfloor;
    }

    public void setBidfloor(Double bidfloor) {
        this.bidfloor = bidfloor;
    }

    public Integer getSecure() {
        return secure;
    }

    public void setSecure(Integer secure) {
        this.secure = secure;
    }
}
