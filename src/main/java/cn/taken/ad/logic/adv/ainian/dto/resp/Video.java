package cn.taken.ad.logic.adv.ainian.dto.resp;

import java.util.List;

public class Video {
    private String url; //视频物料地址
    private Integer w; //视频物料宽度
    private Integer h; //视频物料高度
    private Integer duration; //视频播放时长
    private Integer forceDuration; //视频强制播放时长
    private Integer type; //视频类型：0 普通视频；1 激励视频
    private String coverUrl; //视频封面图片
    private List<VideoTrack> videoTracks; //视频跟踪上报地址。

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getW() {
        return w;
    }

    public void setW(Integer w) {
        this.w = w;
    }

    public Integer getH() {
        return h;
    }

    public void setH(Integer h) {
        this.h = h;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getForceDuration() {
        return forceDuration;
    }

    public void setForceDuration(Integer forceDuration) {
        this.forceDuration = forceDuration;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    public List<VideoTrack> getVideoTracks() {
        return videoTracks;
    }

    public void setVideoTracks(List<VideoTrack> videoTracks) {
        this.videoTracks = videoTracks;
    }
}
