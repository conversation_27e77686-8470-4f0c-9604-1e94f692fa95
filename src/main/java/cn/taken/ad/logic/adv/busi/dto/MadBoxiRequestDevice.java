package cn.taken.ad.logic.adv.busi.dto;

import java.io.Serializable;
import java.util.List;

/**
 * MADBOXI设备信息
 */
public class MadBoxiRequestDevice implements Serializable {
    private static final long serialVersionUID = -7064608428216363189L;

    /**
     * 操作系统 1：android；2：ios；3：ott
     */
    private String os;

    /**
     * 操作系统版本
     */
    private String os_version;

    /**
     * 浏览器 user-agent
     */
    private String ua;

    /**
     * 手机终端的 ip
     */
    private String ip;

    /**
     * 手机硬件制造商/设备品牌
     */
    private String make;

    /**
     * 设备型号
     */
    private String model;

    /**
     * 设备屏幕宽度
     */
    private Integer width;

    /**
     * 设备屏幕高度
     */
    private Integer height;

    /**
     * Android 必填。手机的 IMEI
     */
    private String imei;

    /**
     * imei 的 md5 值,32 位小写
     */
    private String imeimd5;

    /**
     * Android 必填。因 android Q 版本无法获取 imei
     */
    private String oaid;

    /**
     * oaid 的 md5 值,32 位小写
     */
    private String oaidMd5;

    /**
     * Android 系统的 AndroidID
     */
    private String androidId;

    /**
     * Android 系统的 AndroidID md5 值
     */
    private String androididMd5;

    /**
     * 设备 WiFi 网卡 MAC 地址
     */
    private String mac;

    /**
     * mac 的 md5 值, 32 位小写
     */
    private String macmd5;

    /**
     * iOS 必填。
     */
    private String idfa;

    /**
     * idfa 的 md5 值,32 位小写
     */
    private String idfaMd5;

    /**
     * iOS 必填。iOS 设备唯一标志码，idfa 关闭时使用
     */
    private String openudid;

    /**
     * iOS 需要中广协 CAID。被废弃，优先使用 caids。
     */
    private String caid;

    /**
     * iOS 需要 caid 对应的版本号。被废弃，优先使用 caids。
     */
    private String caidVer;

    /**
     * 联网方式 0: 其它 1: WIFI，2: 2G，3: 3G，4: 4G
     */
    private Integer connectiontype;

    /**
     * 运营商 0:其它，1:移动，2:联通，3:电信
     */
    private Integer carrier;

    /**
     * 屏幕像素密度，示例：400
     */
    private Integer ppi;

    /**
     * 屏幕物理像素密度，示例：2.5
     */
    private Float pxratio;

    /**
     * 无线网SSID 名称，如获取不到可传空
     */
    private String ssid;

    /**
     * 手机 ROM 版本，如获取不到可传空
     */
    private String romVersion;

    /**
     * 华为 hms core 版本号(华为必填)
     */
    private String hms;

    /**
     * 应用商店版本号
     */
    private String ag;

    /**
     * 系统启动标识
     */
    private String bootMark;

    /**
     * 系统更新标识
     */
    private String updateMark;

    /**
     * 阿里设备标识
     */
    private String aaidIos;

    /**
     * 拼多多设备标识
     */
    private String paid;

    /**
     * Android Advertising ID
     */
    private String aaid;

    /**
     * 设备 machine，caid 必传参数
     */
    private String hardwareMachine;

    /**
     * 设备启动时间，caid 必传参数
     */
    private String startupTime;

    /**
     * 系统版本更新时间，caid 必传参数
     */
    private String mbTime;

    /**
     * 国家，caid 必传参数
     */
    private String countryCode;

    /**
     * 语言，caid 必传参数
     */
    private String language;

    /**
     * 运营商名称 ，caid 必传参数
     */
    private String carrierName;

    /**
     * 内存空间，字节，caid 必传参数
     */
    private Integer memTotal;

    /**
     * 磁盘总空间，字节，caid 必传参数
     */
    private Integer diskTotal;

    /**
     * 时区，caid 必传参数
     */
    private String localTzName;

    /**
     * 设备 model，caid 必传参数
     */
    private String hardwareModel;

    /**
     * 系统版本，caid 必传参数
     */
    private String iosOsVersion;

    /**
     * 设备名称（小写 MD5) ，caid 必传参数
     */
    private String phoneName;

    /**
     * 广告授权情况
     */
    private String authStatus;

    /**
     * cpu 数目
     */
    private String cpuNum;

    /**
     * app 包名列表（多个以逗号分割例如："com.xxx1,com.xxx2"）
     */
    private String appList;

    /**
     * 位置信息
     */
    private MadBoxiRequestGeo geo;

    /**
     * 设备初始化时间，birth_time、device_filetime 传其一，优先传 device_filetime。
     */
    private String birthTime;

    /**
     * 设备初始化时间，单位秒保留 9 位小数。格式:"1649783466.444164583"
     */
    private String deviceFiletime;

    /**
     * 设备系统启动时间,精度毫秒
     */
    private String bootTimeMillisec;

    /**
     * 设备系统更新时间, 精度纳秒，格式为：1621329481::433642666, :前部分为秒，:后部分为纳秒
     */
    private String updateTimeNanosec;

    /**
     * caid 数组，iOS 需要
     */
    private List<MadBoxiRequestCaid> caids;

    // Getters and Setters
    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public String getUa() {
        return ua;
    }

    public void setUa(String ua) {
        this.ua = ua;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getMake() {
        return make;
    }

    public void setMake(String make) {
        this.make = make;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getImeimd5() {
        return imeimd5;
    }

    public void setImeimd5(String imeimd5) {
        this.imeimd5 = imeimd5;
    }

    public String getOaid() {
        return oaid;
    }

    public void setOaid(String oaid) {
        this.oaid = oaid;
    }

    public String getOaidMd5() {
        return oaidMd5;
    }

    public void setOaidMd5(String oaidMd5) {
        this.oaidMd5 = oaidMd5;
    }

    public String getAndroidId() {
        return androidId;
    }

    public void setAndroidId(String androidId) {
        this.androidId = androidId;
    }

    public String getAndroididMd5() {
        return androididMd5;
    }

    public void setAndroididMd5(String androididMd5) {
        this.androididMd5 = androididMd5;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getMacmd5() {
        return macmd5;
    }

    public void setMacmd5(String macmd5) {
        this.macmd5 = macmd5;
    }

    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        this.idfa = idfa;
    }

    public String getIdfaMd5() {
        return idfaMd5;
    }

    public void setIdfaMd5(String idfaMd5) {
        this.idfaMd5 = idfaMd5;
    }

    public String getOpenudid() {
        return openudid;
    }

    public void setOpenudid(String openudid) {
        this.openudid = openudid;
    }

    public String getCaid() {
        return caid;
    }

    public void setCaid(String caid) {
        this.caid = caid;
    }

    public String getCaidVer() {
        return caidVer;
    }

    public void setCaidVer(String caidVer) {
        this.caidVer = caidVer;
    }

    public Integer getConnectiontype() {
        return connectiontype;
    }

    public void setConnectiontype(Integer connectiontype) {
        this.connectiontype = connectiontype;
    }

    public Integer getCarrier() {
        return carrier;
    }

    public void setCarrier(Integer carrier) {
        this.carrier = carrier;
    }

    public Integer getPpi() {
        return ppi;
    }

    public void setPpi(Integer ppi) {
        this.ppi = ppi;
    }

    public Float getPxratio() {
        return pxratio;
    }

    public void setPxratio(Float pxratio) {
        this.pxratio = pxratio;
    }

    public String getSsid() {
        return ssid;
    }

    public void setSsid(String ssid) {
        this.ssid = ssid;
    }

    public String getRomVersion() {
        return romVersion;
    }

    public void setRomVersion(String romVersion) {
        this.romVersion = romVersion;
    }

    public String getHms() {
        return hms;
    }

    public void setHms(String hms) {
        this.hms = hms;
    }

    public String getAg() {
        return ag;
    }

    public void setAg(String ag) {
        this.ag = ag;
    }

    public String getBootMark() {
        return bootMark;
    }

    public void setBootMark(String bootMark) {
        this.bootMark = bootMark;
    }

    public String getUpdateMark() {
        return updateMark;
    }

    public void setUpdateMark(String updateMark) {
        this.updateMark = updateMark;
    }

    public String getAaidIos() {
        return aaidIos;
    }

    public void setAaidIos(String aaidIos) {
        this.aaidIos = aaidIos;
    }

    public String getPaid() {
        return paid;
    }

    public void setPaid(String paid) {
        this.paid = paid;
    }

    public String getAaid() {
        return aaid;
    }

    public void setAaid(String aaid) {
        this.aaid = aaid;
    }

    public String getHardwareMachine() {
        return hardwareMachine;
    }

    public void setHardwareMachine(String hardwareMachine) {
        this.hardwareMachine = hardwareMachine;
    }

    public String getStartupTime() {
        return startupTime;
    }

    public void setStartupTime(String startupTime) {
        this.startupTime = startupTime;
    }

    public String getMbTime() {
        return mbTime;
    }

    public void setMbTime(String mbTime) {
        this.mbTime = mbTime;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getCarrierName() {
        return carrierName;
    }

    public void setCarrierName(String carrierName) {
        this.carrierName = carrierName;
    }

    public Integer getMemTotal() {
        return memTotal;
    }

    public void setMemTotal(Integer memTotal) {
        this.memTotal = memTotal;
    }

    public Integer getDiskTotal() {
        return diskTotal;
    }

    public void setDiskTotal(Integer diskTotal) {
        this.diskTotal = diskTotal;
    }

    public String getLocalTzName() {
        return localTzName;
    }

    public void setLocalTzName(String localTzName) {
        this.localTzName = localTzName;
    }

    public String getHardwareModel() {
        return hardwareModel;
    }

    public void setHardwareModel(String hardwareModel) {
        this.hardwareModel = hardwareModel;
    }

    public String getIosOsVersion() {
        return iosOsVersion;
    }

    public void setIosOsVersion(String iosOsVersion) {
        this.iosOsVersion = iosOsVersion;
    }

    public String getPhoneName() {
        return phoneName;
    }

    public void setPhoneName(String phoneName) {
        this.phoneName = phoneName;
    }

    public String getAuthStatus() {
        return authStatus;
    }

    public void setAuthStatus(String authStatus) {
        this.authStatus = authStatus;
    }

    public String getCpuNum() {
        return cpuNum;
    }

    public void setCpuNum(String cpuNum) {
        this.cpuNum = cpuNum;
    }

    public String getAppList() {
        return appList;
    }

    public void setAppList(String appList) {
        this.appList = appList;
    }

    public MadBoxiRequestGeo getGeo() {
        return geo;
    }

    public void setGeo(MadBoxiRequestGeo geo) {
        this.geo = geo;
    }

    public String getBirthTime() {
        return birthTime;
    }

    public void setBirthTime(String birthTime) {
        this.birthTime = birthTime;
    }

    public String getDeviceFiletime() {
        return deviceFiletime;
    }

    public void setDeviceFiletime(String deviceFiletime) {
        this.deviceFiletime = deviceFiletime;
    }

    public String getBootTimeMillisec() {
        return bootTimeMillisec;
    }

    public void setBootTimeMillisec(String bootTimeMillisec) {
        this.bootTimeMillisec = bootTimeMillisec;
    }

    public String getUpdateTimeNanosec() {
        return updateTimeNanosec;
    }

    public void setUpdateTimeNanosec(String updateTimeNanosec) {
        this.updateTimeNanosec = updateTimeNanosec;
    }

    public List<MadBoxiRequestCaid> getCaids() {
        return caids;
    }

    public void setCaids(List<MadBoxiRequestCaid> caids) {
        this.caids = caids;
    }
}
