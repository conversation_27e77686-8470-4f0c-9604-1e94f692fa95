package cn.taken.ad.logic.adv.leyou;

import cn.taken.ad.component.http.apache.FastHttpClient;
import cn.taken.ad.component.http.apache.HttpResult;
import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.encryption.Aes;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.constant.business.ActionType;
import cn.taken.ad.constant.business.CaidVendor;
import cn.taken.ad.constant.business.CarrierType;
import cn.taken.ad.constant.business.ConnectionType;
import cn.taken.ad.constant.business.CoordinateType;
import cn.taken.ad.constant.business.DeviceType;
import cn.taken.ad.constant.business.EventType;
import cn.taken.ad.constant.business.MacroType;
import cn.taken.ad.constant.business.MaterialType;
import cn.taken.ad.constant.business.OsType;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.logic.AdvProcessor;
import cn.taken.ad.logic.adv.leyou.dto.Request;
import cn.taken.ad.logic.adv.leyou.dto.Response;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbReqAdvBillParam;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestAppDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestCaidDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestDeviceDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestGeoDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestInstalledAppDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestNetworkDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestTagDto;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseAppDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseMiniProgramDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseTrackDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseVideoDto;
import cn.taken.ad.logic.base.rtb.response.dto.TagResponseDto;
import cn.taken.ad.utils.ParamParser;
import cn.taken.ad.utils.time.TimeUtils;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * 乐游
 */
@Component("LEYOU" + LogicSuffix.ADV_LOGIC_SUFFIX)
public class LeYouAdvProcessor implements AdvProcessor {

    public static final String API_VERSION = "apiVersion";
    public static final String API_TYPE = "apiType";

    private static final Logger log = LoggerFactory.getLogger(LeYouAdvProcessor.class);

    @Override
    public RtbResponseDto reqAdv(FastHttpClient httpClient, RtbRequestDto rtbDto, RtbAdvDto advDto) throws Throwable {

        Request.Builder builder = protobufRequest(rtbDto, advDto);
        Request request = builder.build();
        advDto.setReqObj(request);
        byte[] reqBytes = request.toByteArray();
        // gzip 压缩json
        HttpResult httpResult = httpClient.postBytes(advDto.getRtburl(), reqBytes, new Header[]{new BasicHeader("Content-Type", "application/x-protobuf")}, advDto.getTimeout());
        RtbResponseDto fail = isHttpRequestFail(httpResult);
        if (fail != null) {
            advDto.setRespObj(fail);
            return fail;
        }
        return parseProtobubfResponse(rtbDto, httpResult, advDto);
    }

    private Request.Builder protobufRequest(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        Request.Builder request = Request.newBuilder();
        Map<String, String> param = ParamParser.parseParamByJson(advDto.getPnyParam());

        request.setApiVersion(param.get(API_VERSION));
        request.setApiType(param.get(API_TYPE));
        request.setRequestId(rtbDto.getReqId());
        if (StringUtils.isNotEmpty(rtbDto.getDevice().getUserAgent())) {
            request.setUa(rtbDto.getDevice().getUserAgent());
        }
        //广告位
        request.setAdSlot(builderProtobufSlot(rtbDto, advDto));
        request.setApp(builderProtobufApp(rtbDto, advDto));
        // device
        request.setDevice(builderProtobufDevice(rtbDto, advDto));
        // network
        request.setNetwork(builderProtobufNetWork(rtbDto));
        //geo
        request.setGeo(builderProtobufGeo(rtbDto));
        //user
        request.setUser(builderProtobufUser(rtbDto));

        return request;
    }

    private Request.User.Builder builderProtobufUser(RtbRequestDto rtbDto) {
        Request.User.Builder user = Request.User.newBuilder();
        List<RequestInstalledAppDto> appInfo = rtbDto.getDevice().getInstalledAppInfo();
        if (null != appInfo && !appInfo.isEmpty()) {
            appInfo.forEach(a -> {
                user.addAppList(JsonHelper.toJsonString(a));
            });
        }
        // 暂无 page ⽤户当前所在⻚⾯URL
        return user;
    }

    private Request.Geo.Builder builderProtobufGeo(RtbRequestDto rtbDto) {

        Request.Geo.Builder geo = Request.Geo.newBuilder();
        RequestGeoDto geoDto = rtbDto.getGeo();
        if (geoDto != null) {
            CoordinateType coordinateType = geoDto.getCoordinateType();
            if (coordinateType != null) {
                switch (coordinateType) {
                    case GLOBAL:
                        geo.setCoordinateType(Request.Geo.CoordinateType.WGS84);
                        break;
                    case STATE:
                        geo.setCoordinateType(Request.Geo.CoordinateType.GCJ02);
                        break;
                    case BAIDU:
                        geo.setCoordinateType(Request.Geo.CoordinateType.BD09);
                        break;
                }
            }
            if (null != geoDto.getLatitude()) {
                geo.setLat(geoDto.getLatitude().floatValue());
            }
            if (null != geoDto.getLongitude()) {
                geo.setLng(geoDto.getLongitude().floatValue());
            }
            if (null != geoDto.getTimestamp()) {
                geo.setCoordTime(geoDto.getTimestamp());
            }
        }
        return geo;
    }

    private Request.Network.Builder builderProtobufNetWork(RtbRequestDto rtbDto) {
        RequestNetworkDto networkDto = rtbDto.getNetwork();
        Request.Network.Builder request = Request.Network.newBuilder();

        if (StringUtils.isNotEmpty(networkDto.getIp())) {
            request.setIp(networkDto.getIp());
        }
        if (StringUtils.isNotEmpty(networkDto.getIpv6())) {
            request.setIpV6(networkDto.getIpv6());
        }

        ConnectionType connectionType = networkDto.getConnectType();
        switch (connectionType) {
            case WIFI:
                request.setConnectionType("WIFI");
                break;
            case NETWORK_2G:
                request.setConnectionType("2G");
                break;
            case NETWORK_3G:
                request.setConnectionType("3G");
                break;
            case NETWORK_4G:
                request.setConnectionType("4G");
                break;
            case NETWORK_5G:
                request.setConnectionType("5G");
                break;
            case ETHERNET:
                request.setConnectionType("ETHERNET");
                break;
            case NETWORK_CELLULAR:
                request.setConnectionType("CELL_UNKNOWN");
                break;
            default:
                request.setConnectionType("UNKNOWN");
                break;
        }

        CarrierType carrierType = networkDto.getCarrierType();
        switch (carrierType) {
            case CM:
                request.setOperatorType("CMCC");
                break;
            case CU:
                request.setOperatorType("CUCC");
                break;
            case CT:
                request.setOperatorType("CTCC");
                break;
            default:
                request.setOperatorType("UNKNOWN");
                break;
        }

        Request.Network.WiFiAp.Builder wifiap = Request.Network.WiFiAp.newBuilder();
        if (StringUtils.isNotEmpty(networkDto.getWifiMac())) {
            wifiap.setApMac(networkDto.getWifiMac());
        }
        request.addWifiAps(wifiap);

        return request;
    }


    private Request.Device.Builder builderProtobufDevice(RtbRequestDto rtbDto, RtbAdvDto advDto) {

        Request.Device.Builder device = Request.Device.newBuilder();
        RequestDeviceDto deviceDto = rtbDto.getDevice();
        RequestNetworkDto networkDto = rtbDto.getNetwork();

        if (StringUtils.isNotEmpty(deviceDto.getIdfa())) {
            device.setIdfa(deviceDto.getIdfa());
        }
        if (StringUtils.isNotEmpty(deviceDto.getIdfaMd5())) {
            device.setIdfaMd5(deviceDto.getIdfaMd5().toLowerCase());
        }

        if (null != deviceDto.getCaids() && !deviceDto.getCaids().isEmpty()) {
            for (RequestCaidDto caid : deviceDto.getCaids()) {
                if (StringUtils.isNotEmpty(caid.getCaid())) {
                    Request.Device.Caid.Builder cd = Request.Device.Caid.newBuilder();
                    if (StringUtils.isNotEmpty(caid.getCaid())) {
                        cd.setId(caid.getCaid());
                    }
                    if (StringUtils.isNotEmpty(caid.getVersion())) {
                        cd.setVersion(StringUtils.isNotEmpty(caid.getVersion()) ? caid.getVersion() : "");
                    }
                    if (null != caid.getVendor()) {
                        CaidVendor vendor = caid.getVendor();
                        cd.setVendorValue(vendor.getType());
                    }
                    if (null != caid.getTimestamp()) {
                        cd.setGenerateTime(caid.getTimestamp());
                    }
                    device.addCaid(cd);
                }
            }
        }

        if (StringUtils.isNotEmpty(deviceDto.getIdfv())) {
            device.setIdfv(deviceDto.getIdfv());
        }

        if (StringUtils.isNotEmpty(deviceDto.getImei())) {
            device.setImei(deviceDto.getImei());
        }
        if (StringUtils.isNotEmpty(deviceDto.getImeiMd5())) {
            device.setImeiMd5(deviceDto.getImeiMd5().toLowerCase());
        }
        if (StringUtils.isNotEmpty(deviceDto.getOaid())) {
            device.setOaid(deviceDto.getOaid());
        }
        if (StringUtils.isNotEmpty(deviceDto.getOaidMd5())) {
            device.setOaidMd5(deviceDto.getOaidMd5().toLowerCase());
        }
        if (StringUtils.isNotEmpty(deviceDto.getVaid())) {
            device.setVaid(deviceDto.getVaid());
        }

        if (StringUtils.isNotEmpty(deviceDto.getAndroidId())) {
            device.setAndroidId(deviceDto.getAndroidId());
        }
        if (StringUtils.isNotEmpty(deviceDto.getAndroidIdMd5())) {
            device.setAndroidIdMd5(deviceDto.getAndroidIdMd5().toLowerCase());
        }

        if (StringUtils.isNotEmpty(networkDto.getMac())) {
            device.setMac(networkDto.getMac());
        }
        if (StringUtils.isNotEmpty(networkDto.getMacMd5())) {
            device.setMacMd5(networkDto.getMacMd5().toLowerCase());
        }
        if (StringUtils.isNotEmpty(deviceDto.getOpenUdId())) {
            device.setOpenudid(deviceDto.getOpenUdId());
        }

        if (StringUtils.isNotEmpty(deviceDto.getImsi())) {
            device.setImsi(deviceDto.getImsi());
        }


        DeviceType deviceType = deviceDto.getDeviceType();
        if (null == deviceType) {
            device.setDeviceTypeValue(1);
        } else {
            switch (deviceType) {
                case PAD:
                    device.setDeviceTypeValue(2);
                    break;
                case PHONE:
                    device.setDeviceTypeValue(1);
                    break;
                case PC:
                    device.setDeviceTypeValue(1);
                    break;
                case TV:
                    device.setDeviceTypeValue(3);
                    break;
                case UNKNOWN:
                    device.setDeviceTypeValue(1);
                    break;
                default:
                    device.setDeviceTypeValue(1);
                    break;
            }
        }


        OsType osType = deviceDto.getOsType();
        if (null != osType) {
            switch (osType) {
                case IOS:
                    device.setOsType("IOS");
                    break;
                case ANDROID:
                    device.setOsType("ANDROID");
                    break;
            }
        }
        device.setOsVersion(StringUtils.isNotEmpty(deviceDto.getOsVersion()) ? deviceDto.getOsVersion() : "");
        if (null != deviceDto.getApiLevel()) {
            device.setApiLevel(deviceDto.getApiLevel());
        }

        if (StringUtils.isNotEmpty(deviceDto.getVendor())) {
            device.setManufacturer(deviceDto.getVendor());
        }

        if (StringUtils.isNotEmpty(deviceDto.getBrand())) {
            device.setBrand(deviceDto.getBrand());
        }
        if (StringUtils.isNotEmpty(deviceDto.getModel())) {
            device.setModel(deviceDto.getModel());
        }
        if (null != deviceDto.getWidth()) {
            device.setScreenWidth(deviceDto.getWidth());
        }
        if (null != deviceDto.getHeight()) {
            device.setScreenHeight(deviceDto.getHeight());
        }

        if (null != deviceDto.getScreenDensity()) {
            device.setDensity(deviceDto.getScreenDensity().floatValue());
        }
        if (null != deviceDto.getPpi()) {
            device.setPpi(deviceDto.getPpi());
        }

        if (null != deviceDto.getOrientation()) {
            if (deviceDto.getOrientation().getType() == 1) {
                device.setOrientationValue(2);
            } else if (deviceDto.getOrientation().getType() == 2) {
                device.setOrientationValue(1);
            } else if (deviceDto.getOrientation().getType() == 999) {
                device.setOrientationValue(0);
            }
        }

        if (StringUtils.isNotEmpty(deviceDto.getHmsVersion())) {
            device.setHmsCore(deviceDto.getHmsVersion());
        }
        if (StringUtils.isNotEmpty(deviceDto.getRomVersion())) {
            device.setRomVersion(deviceDto.getRomVersion());
        }

        if (StringUtils.isNotEmpty(deviceDto.getAppStoreVersion())) {
            device.setAppstoreVersion(deviceDto.getAppStoreVersion());
        }

        Long startTime = TimeUtils.convertMilliSecond(deviceDto.getSysStartTime());
        if (null != startTime) {
            device.setDeviceStartSec(startTime / 1000 + "");
        }
        if (StringUtils.isNotEmpty(deviceDto.getDeviceName())) {
            device.setDeviceName(deviceDto.getDeviceName());
        }
        if (StringUtils.isNotEmpty(deviceDto.getDeviceNameMd5())) {
            device.setDeviceNameMd5(deviceDto.getDeviceNameMd5().toLowerCase());
        }

        if (StringUtils.isNotEmpty(deviceDto.getHardwareMachine())) {
            device.setHardwareMachine(deviceDto.getHardwareMachine());
        }
        if (null != deviceDto.getDeviceMemory()) {
            device.setPhysicalMemoryByte(String.valueOf(deviceDto.getDeviceMemory().longValue()));
        }
        if (null != deviceDto.getDeviceHardDisk()) {
            device.setHarddiskSizeByte(String.valueOf(deviceDto.getDeviceHardDisk().longValue()));
        }
        Long updateTime = TimeUtils.convertMilliSecond(deviceDto.getSysUpdateTime());
        if (null != updateTime) {
            String time = TimeUtils.formatUnixTimeWithPrecision(updateTime, false);
            device.setSystemUpdateSec(StringUtils.isNotEmpty(time) ? time : "");
        }

        if (StringUtils.isNotEmpty(deviceDto.getHardwareModel())) {
            device.setHardwareModel(deviceDto.getHardwareModel());
        }

        if (StringUtils.isNotEmpty(deviceDto.getCountry())) {
            device.setCountry(deviceDto.getCountry());
        }

        if (StringUtils.isNotEmpty(deviceDto.getLanguage())) {
            device.setLanguage(deviceDto.getLanguage());
        }

        if (StringUtils.isNotEmpty(deviceDto.getTimeZone())) {
            device.setTimeZone(deviceDto.getTimeZone());
        }

        if (StringUtils.isNotEmpty(deviceDto.getSysElapseTime())) {
            try {
                device.setElapseTime(Long.parseLong(deviceDto.getSysElapseTime()));
            }catch (Exception ignored){}
        }

        if (StringUtils.isNotEmpty(deviceDto.getBootMark())) {
            device.setBootMark(deviceDto.getBootMark());
        }
        if (StringUtils.isNotEmpty(deviceDto.getUpdateMark())) {
            device.setUpdateMark(deviceDto.getUpdateMark());
        }

        if (null != deviceDto.getCpuNum()) {
            device.setSysCpuNum(deviceDto.getCpuNum());
        }

        if (null != deviceDto.getIdfaPolicy()) {
            switch (deviceDto.getIdfaPolicy()) {
                case 1:
                    device.setSysIdfaPolicyValue(0);
                    break;
                case 2:
                    device.setSysIdfaPolicyValue(1);
                    break;
                case 3:
                    device.setSysIdfaPolicyValue(2);
                    break;
                case 4:
                    device.setSysIdfaPolicyValue(3);
                    break;
            }
        }

        if (null != deviceDto.getBatteryStatus()) {
            switch (deviceDto.getBatteryStatus()) {
                case 1:
                    device.setBatteryStateValue(1);
                    break;
                case 2:
                    device.setBatteryStateValue(3);
                    break;
                case 3:
                    device.setBatteryStateValue(2);
                    break;
                case 4:
                    device.setBatteryStateValue(5);
                    break;
            }
        }
        if (null != deviceDto.getBatteryPower()) {
            device.setBattery(deviceDto.getBatteryPower());
        }
        if (StringUtils.isNotEmpty(deviceDto.getSysInitTime())) {
            device.setBirthtime(deviceDto.getSysInitTime());
        }
        if (StringUtils.isNotEmpty(deviceDto.getPaid())) {
            device.setPaid(deviceDto.getPaid());
        }
        Long sysComTime = TimeUtils.convertMilliSecond(deviceDto.getSysCompileTime());
        if (null != sysComTime) {
            device.setSysComplingTime(sysComTime);
        }

        if (StringUtils.isNotEmpty(networkDto.getSsid())) {
            device.setSsid(networkDto.getSsid());
        }
        if (null != deviceDto.getSkanVersion() && !deviceDto.getSkanVersion().isEmpty()) {
            deviceDto.getSkanVersion().forEach(s -> {
                if (StringUtils.isNotEmpty(s)){
                    device.addSkanVersion(s);
                }
            });
        }

        return device;
    }

    private Request.AdSlot.Builder builderProtobufSlot(RtbRequestDto rtbDto, RtbAdvDto advDto) {

        Request.AdSlot.Builder adSlot = Request.AdSlot.newBuilder();

        RequestTagDto tagDto = rtbDto.getTag();
        if (StringUtils.isNotEmpty(advDto.getTagCode())) {
            adSlot.setSlotId(advDto.getTagCode());
        }
        if (null != tagDto.getWidth()) {
            adSlot.setWidth(tagDto.getWidth());
        }
        if (null != tagDto.getHeight()) {
            adSlot.setHeight(tagDto.getHeight());
        }
        if (null != tagDto.getMinDuration()) {
            adSlot.setMinDuration(tagDto.getMinDuration());
        }
        if (null != tagDto.getMaxDuration()) {
            adSlot.setMaxDuration(tagDto.getMaxDuration());
        }
        if (null != tagDto.getNeedHttps()) {
            if (tagDto.getNeedHttps()) {
                adSlot.setSupportHttps(1);
            }
        }
        if (null != tagDto.getPrice()) {
            adSlot.setBidFloor(tagDto.getPrice().intValue());
        }
        return adSlot;
    }

    private Request.App.Builder builderProtobufApp(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        Request.App.Builder request = Request.App.newBuilder();
        RequestAppDto appDto = rtbDto.getApp();
        if (StringUtils.isNotEmpty(advDto.getAppCode())) {
            request.setAppId(advDto.getAppCode());
        }
        if (StringUtils.isNotEmpty(appDto.getAppName())) {
            request.setAppName(appDto.getAppName());
        }
        if (StringUtils.isNotEmpty(appDto.getBundle())) {
            request.setPackageName(appDto.getBundle());
        }
        if (StringUtils.isNotEmpty(appDto.getAppVersionCode())) {
            try {
                request.setVersionCode(Integer.parseInt(appDto.getAppVersionCode()));
            } catch (Exception ignore) {

            }
        }
        if (StringUtils.isNotEmpty(appDto.getAppVersion())) {
            request.setVersionName(appDto.getAppVersion());
        }
        return request;
    }

    public RtbResponseDto parseProtobubfResponse(RtbRequestDto rtbDto, HttpResult httpResult, RtbAdvDto advDto) throws Exception {

        byte[] data = httpResult.getData();
        if (null == data || data.length == 0) {
            log.info("RtbId:{} Req Adv:{} Resp Empty ", rtbDto.getReqId(), advDto.getTagCode());
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), "Empty resp");
        }
        Response response = Response.parseFrom(data);
        advDto.setRespObj(response);

        if (response.getCode() != 0 || response.getAdsList().isEmpty()) {
            return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充", response.getCode() + "");
        }
        List<TagResponseDto> tags = new ArrayList<>();
        RtbResponseDto responseDto = new RtbResponseDto(LogicState.SUCCESS.getCode(), "", "", tags);
        response.getAdsList().forEach(tag -> {
            TagResponseDto tagResponseDto = new TagResponseDto();

            String price = String.valueOf(tag.getPrice());

            if (StringUtils.isNotEmpty(tag.getTitle())) {
                tagResponseDto.setTitle(tag.getTitle());
            }
            if (StringUtils.isNotEmpty(tag.getDesc())) {
                tagResponseDto.setDesc(tag.getDesc());
            }
            if (StringUtils.isNotEmpty(tag.getIcon())) {
                tagResponseDto.setIconUrl(tag.getIcon());
            }


            // 暂无 provider ⼴告提供商标识
            // adType
            switch (tag.getCreativeType()) {
                case 1:
                    tagResponseDto.setMaterialType(MaterialType.IMAGE_TEXT);
                    break;
                case 2:
                    tagResponseDto.setMaterialType(MaterialType.IMAGE_TEXT);
                    break;
                case 3:
                    tagResponseDto.setMaterialType(MaterialType.VIDEO);
                    break;
            }

            if (!tag.getImagesList().isEmpty()) {
                List<String> images = new ArrayList<>();
                tag.getImagesList().forEach(item -> {
                    if (StringUtils.isNotEmpty(item.getUrl())) {
                        images.add(item.getUrl());
                    }
                });
                if (!images.isEmpty()) {
                    tagResponseDto.setImgUrls(images);
                }
            }


            Response.Ad.Video video = tag.getVideo();
            if (StringUtils.isNotEmpty(video.getUrl())) {
                ResponseVideoDto videoDto = new ResponseVideoDto();
                videoDto.setVideoUrl(video.getUrl());
                videoDto.setVideoWidth(video.getWidth());
                videoDto.setVideoHeight(video.getHeight());
                if (video.getSize() > 0) {
                    videoDto.setVideoSize(video.getSize());
                }
                if (video.getDuration() > 0) {
                    videoDto.setDuration(video.getDuration());
                }
                if (StringUtils.isNotEmpty(video.getCoverImage())) {
                    videoDto.setCoverImgUrls(new ArrayList<>(Collections.singletonList(video.getCoverImage())));
                }
                tagResponseDto.setVideoInfo(videoDto);
            }
            if (StringUtils.isNotEmpty(tag.getHtmlSnippet())) {
                tagResponseDto.setHtmlContent(tag.getHtmlSnippet());
            }
            //bid_mode
            if (tag.getPrice() > 0) {
                tagResponseDto.setPrice((double) tag.getPrice());
            }

            tagResponseDto.setActionType(ActionType.WEB_VIEW_H5);
            switch (tag.getInteractType()) {
                case 1:
                    tagResponseDto.setActionType(ActionType.WEB_VIEW_H5);
                    break;
                case 2:
                    tagResponseDto.setActionType(ActionType.DOWNLOAD);
                    break;
            }
            if (StringUtils.isNotEmpty(tag.getLandingPageUrl())) {
                tagResponseDto.setClickUrl(tag.getLandingPageUrl());
            }
            if (StringUtils.isNotEmpty(tag.getDeeplinkUrl())) {
                tagResponseDto.setDeepLinkUrl(tag.getDeeplinkUrl());
            }

            if (StringUtils.isNotEmpty(tag.getMarketUrl())) {
                tagResponseDto.setMarketUrl(tag.getMarketUrl());
            }
            if (StringUtils.isNotEmpty(tag.getUniversalLink())) {
                tagResponseDto.setUniversalLink(tag.getUniversalLink());
            }

            ResponseMiniProgramDto mini = new ResponseMiniProgramDto();
            if (StringUtils.isNotEmpty(tag.getWechatAppUsername())) {
                mini.setName(tag.getWechatAppUsername());
            }

            if (StringUtils.isNotEmpty(tag.getWechatAppPath())) {
                mini.setPath(tag.getWechatAppPath());
            }
            tagResponseDto.setMiniProgram(mini);

            ResponseAppDto appDto = new ResponseAppDto();
            if (StringUtils.isNotEmpty(tag.getPackageName())) {
                appDto.setPackageName(tag.getPackageName());
            }
            if (StringUtils.isNotEmpty(tag.getAppName())) {
                appDto.setAppName(tag.getAppName());
            }
            if (StringUtils.isNotEmpty(tag.getAppIcon())) {
                appDto.setAppIconUrl(tag.getAppIcon());
            }
            if (tag.getAppSize() > 0) {
                appDto.setAppSize(tag.getAppSize() * 1024);
            }
            if (StringUtils.isNotEmpty(tag.getAppDeveloper())) {
                appDto.setAppDeveloper(tag.getAppDeveloper());
            }
            if (StringUtils.isNotEmpty(tag.getAppDescUrl())) {
                appDto.setAppInfoUrl(tag.getAppDescUrl());
            }
            if (StringUtils.isNotEmpty(tag.getAppPermissionsUrl())) {
                appDto.setAppPermissionInfoUrl(tag.getAppPermissionsUrl());
            }
            if (StringUtils.isNotEmpty(tag.getAppPrivacyPolicy())) {
                appDto.setAppPrivacyUrl(tag.getAppPrivacyPolicy());
            }
            if (StringUtils.isNotEmpty(tag.getAppVersionName())) {
                appDto.setAppVersion(tag.getAppVersionName());
            }
            if (StringUtils.isNotEmpty(tag.getAppNumber())) {
                appDto.setRecordNumber(tag.getAppNumber());
            }
            tagResponseDto.setAppInfo(appDto);

            List<ResponseTrackDto> tracks = new ArrayList<>();
            tagResponseDto.setTracks(tracks);

            List<Response.Ad.Tracking> respTracks = tag.getTrackingsList();
            if (!respTracks.isEmpty()) {
                respTracks.forEach(respTrack -> {
                    ResponseTrackDto dto = convertProtobufTrack(tagResponseDto, respTrack);
                    if (null != dto) {
                        tracks.add(dto);
                    }
                });
            }

            // 宏替换 统一替换成平台的宏(事件)
            tagResponseDto.getTracks().forEach(track -> {
                List<String> urls = track.getTrackUrls();
                urls = replaceMacro("__TS__", urls, MacroType.TIME.getCode());
                urls = replaceMacro("__SECONDS__", urls, MacroType.TIME_SECONDS.getCode());
                if (StringUtils.isNotBlank(price)) {
                    urls = replaceMacro("__WIN_PRICE__", urls, price);
                }

                urls = replaceMacro("__END_TS__", urls, MacroType.END_TIME.getCode());
                urls = replaceMacro("__SECONDS__", urls, MacroType.END_TIME_SECONDS.getCode());
                urls = replaceMacro("__IP__", urls, MacroType.IP.getCode());
                urls = replaceMacro("__DP_WIDTH__", urls, MacroType.DP_WIDTH.getCode());
                urls = replaceMacro("__DP_HEIGHT__", urls, MacroType.DP_HEIGHT.getCode());
                urls = replaceMacro("__DP_DOWN_X__", urls, MacroType.DP_DOWN_X.getCode());
                urls = replaceMacro("__DP_DOWN_Y__", urls, MacroType.DP_DOWN_Y.getCode());
                urls = replaceMacro("__DP_UP_X__", urls, MacroType.DP_UP_X.getCode());
                urls = replaceMacro("__DP_UP_Y__", urls, MacroType.DP_UP_Y.getCode());

                urls = replaceMacro("__X_MAX_ACC__", urls, MacroType.X_MAX_ACC.getCode());
                urls = replaceMacro("__Y_MAX_ACC__", urls, MacroType.Y_MAX_ACC.getCode());
                urls = replaceMacro("__Z_MAX_ACC__", urls, MacroType.Z_MAX_ACC.getCode());

                urls = replaceMacro("__TURN_TIME__", urls, MacroType.TURN_TIME.getCode());
                urls = replaceMacro("__SLD__", urls, MacroType.SLD.getCode());

                urls = replaceMacro("__WIDTH__", urls, MacroType.WIDTH.getCode());
                urls = replaceMacro("__HEIGHT__", urls, MacroType.HEIGHT.getCode());

                urls = replaceMacro("__DOWN_X__", urls, MacroType.DOWN_X.getCode());
                urls = replaceMacro("__DOWN_Y__", urls, MacroType.DOWN_Y.getCode());
                urls = replaceMacro("__UP_X__", urls, MacroType.UP_X.getCode());
                urls = replaceMacro("__UP_Y__", urls, MacroType.UP_Y.getCode());

                urls = replaceMacro("__S_DOWN_X__", urls, MacroType.ABS_DOWN_X.getCode());
                urls = replaceMacro("__S_DOWN_Y__", urls, MacroType.ABS_DOWN_Y.getCode());
                urls = replaceMacro("__S_UP_X__", urls, MacroType.ABS_UP_X.getCode());
                urls = replaceMacro("__S_UP_Y__", urls, MacroType.ABS_UP_Y.getCode());


                urls = replaceMacro("__P_RATE__", urls, MacroType.VIDEO_P_RATE.getCode());
                urls = replaceMacro("__PROGRESS__", urls, MacroType.VIDEO_PROGRESS_SEC.getCode());

                urls = replaceMacro("__PROGRESS_MS__", urls, MacroType.VIDEO_PROGRESS.getCode());

                urls = replaceMacro("__PROGRESS_MS__", urls, MacroType.VIDEO_PROGRESS.getCode());


                urls = replaceMacro("__VIDEO_TIME__", urls, MacroType.VIDEO_TIME.getCode());

                urls = replaceMacro("__VIDEO_TIME__", urls, MacroType.VIDEO_TIME.getCode());


                urls = replaceMacro("__BEGIN_TIME__", urls, MacroType.VIDEO_BEGIN_TIME.getCode());
                urls = replaceMacro("__END_TIME__", urls, MacroType.VIDEO_END_TIME.getCode());

                urls = replaceMacro("__PLAY_FIRST_FRAME__", urls, MacroType.VIDEO_PLAY_FIRST_FRAME.getCode());
                urls = replaceMacro("__PLAY_LAST_FRAME__", urls, MacroType.VIDEO_PLAY_LAST_FRAME.getCode());

                urls = replaceMacro("__TYPE__", urls, MacroType.VIDEO_TYPE.getCode());
                urls = replaceMacro("__BEHAVIOR__", urls, MacroType.VIDEO_BEHAVIOR.getCode());

                urls = replaceMacro("__STATUS__", urls, MacroType.VIDEO_STATUS.getCode());

                urls = replaceMacro("__TARGET_APP_INSTALL__", urls, MacroType.TARGET_APP_INSTALL.getCode());


                track.setTrackUrls(urls);
            });

            tags.add(tagResponseDto);
        });
        return responseDto;
    }

    private ResponseTrackDto convertProtobufTrack(TagResponseDto tagResponseDto, Response.Ad.Tracking track) {
        EventType eventType = convertEventType(track);
        if (track.getUrlsList().isEmpty()) {
            return null;
        }
        List<String> urls = new ArrayList<>();
        for (String url : track.getUrlsList()) {
            urls.add(url);
        }
        if (null == eventType) {
            String event = track.getEvent();
            if (event.equals("win_notice")) {
                tagResponseDto.setWinNoticeUrls(urls);
            } else if (event.equals("loss_notice")) {
                tagResponseDto.setFailNoticeUrls(urls);
            } else if (event.equals("notice")) {
                tagResponseDto.setWinNoticeUrls(urls);
                tagResponseDto.setFailNoticeUrls(urls);
            }
            return null;
        }

        ResponseTrackDto dto = new ResponseTrackDto();
        dto.setTrackUrls(urls);
        dto.setTrackType(eventType.getType());
        return dto;
    }

    private EventType convertEventType(Response.Ad.Tracking track) {
        if (null == track) {
            return null;
        }
        String event = track.getEvent();
        if (event.equals("impression")) {
            return EventType.EXPOSURE;
        } else if (event.equals("click")) {
            return EventType.CLICK;
        } else if (event.equals("close")) {
            return EventType.CLOSE_AD;
        } else if (event.equals("skip")) {
            return EventType.VIDEO_SKIP;
        } else if (event.equals("down_start")) {
            return EventType.DOWNLOAD_BEGIN;
        } else if (event.equals("down_end")) {
            return EventType.DOWNLOAD_COMPLETED;
        } else if (event.equals("install_start")) {
            return EventType.INSTALL_BEGIN;
        } else if (event.equals("install_end")) {
            return EventType.INSTALL_COMPLETED;
        } else if (event.equals("install_open")) {
            return EventType.INSTALLED_OPEN;
        } else if (event.equals("active")) {
            return EventType.ACTIVE_APP;
        } else if (event.equals("app_installed")) {
            return EventType.APP_INSTALLED;
        } else if (event.equals("app_not_installed")) {
            return EventType.APP_NOT_INSTALL;
        } else if (event.equals("deeplink_invoke")) {
            return EventType.DEEPLINK_START;
        } else if (event.equals("deeplink_success")) {
            return EventType.DEEPLINK_OPEN_SUCCESS;
        } else if (event.equals("deeplink_fail")) {
            return EventType.DEEPLINK_OPEN_FAIL;
        } else if (event.equals("mini_program_success")) {
            return EventType.MINI_PROGRAM_SUCCESS;
        } else if (event.equals("mini_program_fail")) {
            return EventType.MINI_PROGRAM_FAIL;
        } else if (event.equals("video_start")) {
            return EventType.VIDEO_BEGIN;
        } else if (event.equals("video_pause")) {
            return EventType.VIDEO_PAUSE;
        } else if (event.equals("video_resume")) {
            return EventType.VIDEO_CONTINUE;
        } else if (event.equals("video_first_quartile")) {
            return EventType.VIDEO_25;
        } else if (event.equals("video_midpoint")) {
            return EventType.VIDEO_50;
        } else if (event.equals("video_third_quartile")) {
            return EventType.VIDEO_75;
        } else if (event.equals("video_end")) {
            return EventType.VIDEO_END;
        } else if (event.equals("video_mute")) {
            return EventType.VIDEO_MUTE;
        } else if (event.equals("video_unmute")) {
            return EventType.VIDEO_UNMUTE;
        } else if (event.equals("video_fullscreen")) {
            return EventType.VIDEO_FULL_SCREEN;
        } else if (event.equals("video_unfullscreen")) {
            return EventType.VIDEO_EXITS_FULL_SCREEN;
        } else if (event.equals("video_loaded")) {
            return EventType.VIDEO_LOADED_SUCCESS;
        } else if (event.equals("video_play_error")) {
            return EventType.VIDEO_PLAY_ERROR;
        } else if (event.equals("video_upscroll")) {
            return EventType.VIDEO_SLIDE_UP;
        } else if (event.equals("video_downscroll")) {
            return EventType.VIDEO_SLIDE_DOWN;
        } else if (event.equals("video_replay")) {
            return EventType.VIDEO_PLAY_REPLAY;
        }
        return null;
    }


    private String aesPrice(Double price, String aesKey, String ivKey) {
        if (null == price) {
            return null;
        }
        if (StringUtils.isEmpty(aesKey)) {
            return null;
        }
        if (StringUtils.isEmpty(ivKey)) {
            return null;
        }
        byte[] data = Aes.encrypt(price.toString().getBytes(StandardCharsets.UTF_8), aesKey.getBytes(StandardCharsets.UTF_8), ivKey.getBytes(StandardCharsets.UTF_8), "AES/CBC/PKCS5Padding");
        return Base64.getUrlEncoder().encodeToString(data);
    }


    @Override
    public SuperResult<String> billResult(FastHttpClient httpClient, RtbReqAdvBillParam reqDto) throws Exception {
        // 是否有请求成功的
        boolean hasRight = false;
        List<String> urls = reqDto.getUrls();
        if (reqDto.getBiddingSuccess()) {
            if (null != reqDto.getPrice()){
                urls = replaceMacro("__WIN_PRICE__", urls, reqDto.getPrice() + "");
            }
        } else {
            urls = replaceMacro("__AUCTION_LOSS__", urls, "1");
        }
        String msg = "not right";
        for (String url : urls) {
            HttpResult result = httpClient.get(url, null, null, 30000);
            if (result.isSuccess()) {
                hasRight = result.isSuccess();
            } else {
                msg = result.getStatusLine().toString() + (result.getThrowable() != null ? result.getThrowable().getMessage() : "");
            }
            //log.info("Req Adv Bill ReqId:{}, Result:{},HttpCode:{},Header:{},ErrMsg:{},url:{}", reqDto.getRtbId(), result.isSuccess(), result.getStatusLine(), Arrays.toString(result.getHeaders()), null != result.getThrowable() ? result.getThrowable().getMessage() : "", url);
        }
        return hasRight ? SuperResult.rightResult() : SuperResult.badResult(msg);
    }
}
