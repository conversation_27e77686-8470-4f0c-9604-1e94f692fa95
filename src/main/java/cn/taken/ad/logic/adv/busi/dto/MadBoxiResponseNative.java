package cn.taken.ad.logic.adv.busi.dto;

import java.io.Serializable;
import java.util.List;

public class MadBoxiResponseNative implements Serializable {
    // 必填项，资源对象数组
    private List<MadBoxiResponseAsset> assets; // required

    // 必填项，落地页地址
    private String url; // required

    // 可选项，应用唤起地址, 优先尝试调起, 调起失败则打开落地页
    private String deeplink_url;

    // 可选项，Ios 通用链接，ios 优先处理
    private String universal_link;

    // 可选项，应用商店地址
    private String market_url;

    // 可选项，微信小程序原始 id
    private String wx_mini_user;

    // 可选项，微信小程序路径
    private String wx_mini_path;

    // 可选项，展示上报地址
    private List<String> imp_urls;

    // 可选项，点击上报地址
    private List<String> click_urls;

    // 可选项，下载开始上报地址
    private List<String> download_urls;

    // 可选项，下载完成上报地址
    private List<String> downloaded_urls;

    // 可选项，安装完成上报地址
    private List<String> installed_urls;

    // 可选项，安装完成打开上报地址
    private List<String> open_urls;

    // 可选项，deeplink 调起成功
    private List<String> dpsucc_urls;

    // 可选项，deeplink 调起失败
    private List<String> dpfail_urls;

    // 可选项，deeplink 检测到已安装
    private List<String> dpinst_urls;

    // 可选项，deeplink 检测到未安装
    private List<String> dpuninst_urls;

    // 可选项，竞胜上报地址
    private List<String> wurls;

    // 可选项，竞败上报地址
    private List<String> lurls;

    // 可选项，下载 app 名称
    private String app_name;

    // 可选项，下载 app 包名
    private String app_package;

    // 可选项，下载 app 版本
    private String app_version;

    // 可选项，下载 app 的图标
    private String app_icon;

    // 可选项，下载 app 大小, 单位: byte
    private Long app_size;

    // 可选项，下载 app 权限列表
    private String permission_url;

    // 可选项，下载 app 隐私协议
    private String privacy_url;

    // 可选项，下载 app 的开发者名称
    private String developer_name;

    // 可选项，下载 app 的功能介绍
    private String introduction_url;

    // 可选项，下载地址
    private String download_url;

    // 可选项，点击坐标上报，具体参考 4.2
    private String click_area_report_url;

    public List<MadBoxiResponseAsset> getAssets() {
        return assets;
    }

    public void setAssets(List<MadBoxiResponseAsset> assets) {
        this.assets = assets;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getDeeplink_url() {
        return deeplink_url;
    }

    public void setDeeplink_url(String deeplink_url) {
        this.deeplink_url = deeplink_url;
    }

    public String getUniversal_link() {
        return universal_link;
    }

    public void setUniversal_link(String universal_link) {
        this.universal_link = universal_link;
    }

    public String getMarket_url() {
        return market_url;
    }

    public void setMarket_url(String market_url) {
        this.market_url = market_url;
    }

    public String getWx_mini_user() {
        return wx_mini_user;
    }

    public void setWx_mini_user(String wx_mini_user) {
        this.wx_mini_user = wx_mini_user;
    }

    public String getWx_mini_path() {
        return wx_mini_path;
    }

    public void setWx_mini_path(String wx_mini_path) {
        this.wx_mini_path = wx_mini_path;
    }

    public List<String> getImp_urls() {
        return imp_urls;
    }

    public void setImp_urls(List<String> imp_urls) {
        this.imp_urls = imp_urls;
    }

    public List<String> getClick_urls() {
        return click_urls;
    }

    public void setClick_urls(List<String> click_urls) {
        this.click_urls = click_urls;
    }

    public List<String> getDownload_urls() {
        return download_urls;
    }

    public void setDownload_urls(List<String> download_urls) {
        this.download_urls = download_urls;
    }

    public List<String> getDownloaded_urls() {
        return downloaded_urls;
    }

    public void setDownloaded_urls(List<String> downloaded_urls) {
        this.downloaded_urls = downloaded_urls;
    }

    public List<String> getInstalled_urls() {
        return installed_urls;
    }

    public void setInstalled_urls(List<String> installed_urls) {
        this.installed_urls = installed_urls;
    }

    public List<String> getOpen_urls() {
        return open_urls;
    }

    public void setOpen_urls(List<String> open_urls) {
        this.open_urls = open_urls;
    }

    public List<String> getDpsucc_urls() {
        return dpsucc_urls;
    }

    public void setDpsucc_urls(List<String> dpsucc_urls) {
        this.dpsucc_urls = dpsucc_urls;
    }

    public List<String> getDpfail_urls() {
        return dpfail_urls;
    }

    public void setDpfail_urls(List<String> dpfail_urls) {
        this.dpfail_urls = dpfail_urls;
    }

    public List<String> getDpinst_urls() {
        return dpinst_urls;
    }

    public void setDpinst_urls(List<String> dpinst_urls) {
        this.dpinst_urls = dpinst_urls;
    }

    public List<String> getDpuninst_urls() {
        return dpuninst_urls;
    }

    public void setDpuninst_urls(List<String> dpuninst_urls) {
        this.dpuninst_urls = dpuninst_urls;
    }

    public List<String> getWurls() {
        return wurls;
    }

    public void setWurls(List<String> wurls) {
        this.wurls = wurls;
    }

    public List<String> getLurls() {
        return lurls;
    }

    public void setLurls(List<String> lurls) {
        this.lurls = lurls;
    }

    public String getApp_name() {
        return app_name;
    }

    public void setApp_name(String app_name) {
        this.app_name = app_name;
    }

    public String getApp_package() {
        return app_package;
    }

    public void setApp_package(String app_package) {
        this.app_package = app_package;
    }

    public String getApp_version() {
        return app_version;
    }

    public void setApp_version(String app_version) {
        this.app_version = app_version;
    }

    public String getApp_icon() {
        return app_icon;
    }

    public void setApp_icon(String app_icon) {
        this.app_icon = app_icon;
    }

    public Long getApp_size() {
        return app_size;
    }

    public void setApp_size(Long app_size) {
        this.app_size = app_size;
    }

    public String getPermission_url() {
        return permission_url;
    }

    public void setPermission_url(String permission_url) {
        this.permission_url = permission_url;
    }

    public String getPrivacy_url() {
        return privacy_url;
    }

    public void setPrivacy_url(String privacy_url) {
        this.privacy_url = privacy_url;
    }

    public String getDeveloper_name() {
        return developer_name;
    }

    public void setDeveloper_name(String developer_name) {
        this.developer_name = developer_name;
    }

    public String getIntroduction_url() {
        return introduction_url;
    }

    public void setIntroduction_url(String introduction_url) {
        this.introduction_url = introduction_url;
    }

    public String getDownload_url() {
        return download_url;
    }

    public void setDownload_url(String download_url) {
        this.download_url = download_url;
    }

    public String getClick_area_report_url() {
        return click_area_report_url;
    }

    public void setClick_area_report_url(String click_area_report_url) {
        this.click_area_report_url = click_area_report_url;
    }
}
