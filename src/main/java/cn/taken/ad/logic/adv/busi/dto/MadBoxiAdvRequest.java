package cn.taken.ad.logic.adv.busi.dto;

import java.io.Serializable;
import java.util.List;

/**
 * MADBOXI广告请求主体
 */
public class MadBoxiAdvRequest implements Serializable {
    private static final long serialVersionUID = -7064608428216363187L;

    /**
     * API 版本
     */
    private String version;

    /**
     * 请求ID
     */
    private String id;
    /**
     * 广告位 ID
     */
    private String slot_id;
    /**
     * 广告位信息
     */
    private List<MadBoxiRequestAdSlot> imp;

    /**
     * 应用信息
     */
    private MadBoxiRequestApp app;

    /**
     * 设备信息
     */
    private MadBoxiRequestDevice device;
    //是否支持视频素材,“1”:支持，“0”:不支持, 默认为”0”
    private String is_video;
    //    最大超时时长，单位毫秒
    private Integer tmax;

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSlot_id() {
        return slot_id;
    }

    public void setSlot_id(String slot_id) {
        this.slot_id = slot_id;
    }

    public List<MadBoxiRequestImp> getImp() {
        return imp;
    }

    public void setImp(List<MadBoxiRequestImp> imp) {
        this.imp = imp;
    }

    public MadBoxiRequestApp getApp() {
        return app;
    }

    public void setApp(MadBoxiRequestApp app) {
        this.app = app;
    }

    public MadBoxiRequestDevice getDevice() {
        return device;
    }

    public void setDevice(MadBoxiRequestDevice device) {
        this.device = device;
    }

    public String getIs_video() {
        return is_video;
    }

    public void setIs_video(String is_video) {
        this.is_video = is_video;
    }

    public Integer getTmax() {
        return tmax;
    }

    public void setTmax(Integer tmax) {
        this.tmax = tmax;
    }
}
