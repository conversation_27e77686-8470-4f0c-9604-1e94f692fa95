package cn.taken.ad.logic.adv.busi.dto;

import java.io.Serializable;
import java.util.List;

/**
 * MADBOXI设备信息
 */
public class MadBoxiRequestDevice implements Serializable {
    private static final long serialVersionUID = -7064608428216363189L;
    
    /**
     * IMEI
     */
    private String imei;
    
    /**
     * IMEI MD5
     */
    private String imeiMd5;
    
    /**
     * IDFA
     */
    private String idfa;
    
    /**
     * IDFA MD5
     */
    private String idfaMd5;
    
    /**
     * OAID
     */
    private String oaid;
    
    /**
     * OAID MD5
     */
    private String oaidMd5;
    
    /**
     * VAID
     */
    private String vaid;
    
    /**
     * VAID MD5
     */
    private String vaidMd5;
    
    /**
     * Android ID
     */
    private String androidId;
    
    /**
     * Android ID MD5
     */
    private String androidIdMd5;
    
    /**
     * IDFV
     */
    private String idfv;
    
    /**
     * MAC地址
     */
    private String mac;
    
    /**
     * MAC MD5
     */
    private String macMd5;
    
    /**
     * CAID列表
     */
    private List<MadBoxiRequestCaid> caids;
    
    /**
     * IMSI
     */
    private String imsi;
    
    /**
     * User Agent
     */
    private String ua;
    
    /**
     * IP地址
     */
    private String ip;
    
    /**
     * 设备类型 0:未知 1:手机 2:平板 3:电视 4:PC
     */
    private Integer devType;
    
    /**
     * 操作系统 0:未知 1:Android 2:iOS 3:Windows 4:HarmonyOS
     */
    private Integer os;
    
    /**
     * 操作系统版本
     */
    private String osVersion;
    
    /**
     * 厂商
     */
    private String vendor;
    
    /**
     * 品牌
     */
    private String brand;
    
    /**
     * 型号
     */
    private String model;
    
    /**
     * 语言
     */
    private String language;
    
    /**
     * 屏幕方向 0:未知 1:横屏 2:竖屏
     */
    private Integer orientation;
    
    /**
     * PPI
     */
    private Integer ppi;
    
    /**
     * 屏幕宽度
     */
    private Integer screenWidth;
    
    /**
     * 屏幕高度
     */
    private Integer screenHeight;
    
    /**
     * 启动标识
     */
    private String bootMark;
    
    /**
     * 更新标识
     */
    private String updateMark;
    
    /**
     * 国家代码
     */
    private String countryCode;
    
    /**
     * 时区
     */
    private String timeZone;
    
    /**
     * 设备名称MD5
     */
    private String deviceNameMd5;
    
    /**
     * 硬件机型
     */
    private String hardwareMachine;
    
    /**
     * 磁盘总容量
     */
    private Long diskTotal;
    
    /**
     * 内存总容量
     */
    private Long memTotal;
    
    /**
     * 应用商店版本
     */
    private String appStoreVersion;
    
    /**
     * HMS Core版本
     */
    private String hmsCore;
    
    /**
     * MIUI版本
     */
    private String miuiVersion;
    
    /**
     * PAID
     */
    private String paid;
    
    /**
     * AAID
     */
    private String aaid;
    
    /**
     * 系统启动时间
     */
    private String systemStartTime;
    
    /**
     * 系统更新时间
     */
    private String systemUpdateTime;
    
    /**
     * 设备初始化时间
     */
    private String deviceInitTime;

    // Getters and Setters
    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getImeiMd5() {
        return imeiMd5;
    }

    public void setImeiMd5(String imeiMd5) {
        this.imeiMd5 = imeiMd5;
    }

    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        this.idfa = idfa;
    }

    public String getIdfaMd5() {
        return idfaMd5;
    }

    public void setIdfaMd5(String idfaMd5) {
        this.idfaMd5 = idfaMd5;
    }

    public String getOaid() {
        return oaid;
    }

    public void setOaid(String oaid) {
        this.oaid = oaid;
    }

    public String getOaidMd5() {
        return oaidMd5;
    }

    public void setOaidMd5(String oaidMd5) {
        this.oaidMd5 = oaidMd5;
    }

    public String getVaid() {
        return vaid;
    }

    public void setVaid(String vaid) {
        this.vaid = vaid;
    }

    public String getVaidMd5() {
        return vaidMd5;
    }

    public void setVaidMd5(String vaidMd5) {
        this.vaidMd5 = vaidMd5;
    }

    public String getAndroidId() {
        return androidId;
    }

    public void setAndroidId(String androidId) {
        this.androidId = androidId;
    }

    public String getAndroidIdMd5() {
        return androidIdMd5;
    }

    public void setAndroidIdMd5(String androidIdMd5) {
        this.androidIdMd5 = androidIdMd5;
    }

    public String getIdfv() {
        return idfv;
    }

    public void setIdfv(String idfv) {
        this.idfv = idfv;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getMacMd5() {
        return macMd5;
    }

    public void setMacMd5(String macMd5) {
        this.macMd5 = macMd5;
    }

    public List<MadBoxiRequestCaid> getCaids() {
        return caids;
    }

    public void setCaids(List<MadBoxiRequestCaid> caids) {
        this.caids = caids;
    }

    public String getImsi() {
        return imsi;
    }

    public void setImsi(String imsi) {
        this.imsi = imsi;
    }

    public String getUa() {
        return ua;
    }

    public void setUa(String ua) {
        this.ua = ua;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getDevType() {
        return devType;
    }

    public void setDevType(Integer devType) {
        this.devType = devType;
    }

    public Integer getOs() {
        return os;
    }

    public void setOs(Integer os) {
        this.os = os;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public String getVendor() {
        return vendor;
    }

    public void setVendor(String vendor) {
        this.vendor = vendor;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public Integer getOrientation() {
        return orientation;
    }

    public void setOrientation(Integer orientation) {
        this.orientation = orientation;
    }

    public Integer getPpi() {
        return ppi;
    }

    public void setPpi(Integer ppi) {
        this.ppi = ppi;
    }

    public Integer getScreenWidth() {
        return screenWidth;
    }

    public void setScreenWidth(Integer screenWidth) {
        this.screenWidth = screenWidth;
    }

    public Integer getScreenHeight() {
        return screenHeight;
    }

    public void setScreenHeight(Integer screenHeight) {
        this.screenHeight = screenHeight;
    }

    public String getBootMark() {
        return bootMark;
    }

    public void setBootMark(String bootMark) {
        this.bootMark = bootMark;
    }

    public String getUpdateMark() {
        return updateMark;
    }

    public void setUpdateMark(String updateMark) {
        this.updateMark = updateMark;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public String getDeviceNameMd5() {
        return deviceNameMd5;
    }

    public void setDeviceNameMd5(String deviceNameMd5) {
        this.deviceNameMd5 = deviceNameMd5;
    }

    public String getHardwareMachine() {
        return hardwareMachine;
    }

    public void setHardwareMachine(String hardwareMachine) {
        this.hardwareMachine = hardwareMachine;
    }

    public Long getDiskTotal() {
        return diskTotal;
    }

    public void setDiskTotal(Long diskTotal) {
        this.diskTotal = diskTotal;
    }

    public Long getMemTotal() {
        return memTotal;
    }

    public void setMemTotal(Long memTotal) {
        this.memTotal = memTotal;
    }

    public String getAppStoreVersion() {
        return appStoreVersion;
    }

    public void setAppStoreVersion(String appStoreVersion) {
        this.appStoreVersion = appStoreVersion;
    }

    public String getHmsCore() {
        return hmsCore;
    }

    public void setHmsCore(String hmsCore) {
        this.hmsCore = hmsCore;
    }

    public String getMiuiVersion() {
        return miuiVersion;
    }

    public void setMiuiVersion(String miuiVersion) {
        this.miuiVersion = miuiVersion;
    }

    public String getPaid() {
        return paid;
    }

    public void setPaid(String paid) {
        this.paid = paid;
    }

    public String getAaid() {
        return aaid;
    }

    public void setAaid(String aaid) {
        this.aaid = aaid;
    }

    public String getSystemStartTime() {
        return systemStartTime;
    }

    public void setSystemStartTime(String systemStartTime) {
        this.systemStartTime = systemStartTime;
    }

    public String getSystemUpdateTime() {
        return systemUpdateTime;
    }

    public void setSystemUpdateTime(String systemUpdateTime) {
        this.systemUpdateTime = systemUpdateTime;
    }

    public String getDeviceInitTime() {
        return deviceInitTime;
    }

    public void setDeviceInitTime(String deviceInitTime) {
        this.deviceInitTime = deviceInitTime;
    }
}
