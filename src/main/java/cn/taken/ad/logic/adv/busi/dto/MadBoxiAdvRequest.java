package cn.taken.ad.logic.adv.busi.dto;

import java.io.Serializable;
import java.util.List;

/**
 * MADBOXI广告请求主体
 */
public class MadBoxiAdvRequest implements Serializable {
    private static final long serialVersionUID = -7064608428216363187L;
    
    /**
     * API 版本
     */
    private String apiVersion;
    
    /**
     * 请求ID
     */
    private String requestId;
    
    /**
     * 用户信息
     */
    private MadBoxiRequestUser user;
    
    /**
     * 设备信息
     */
    private MadBoxiRequestDevice device;
    
    /**
     * 应用信息
     */
    private MadBoxiRequestApp app;
    
    /**
     * 广告位信息
     */
    private List<MadBoxiRequestAdSlot> slots;
    
    /**
     * 网络信息
     */
    private MadBoxiRequestNetwork network;
    
    /**
     * 地理位置信息
     */
    private MadBoxiRequestGeo geo;

    public String getApiVersion() {
        return apiVersion;
    }

    public void setApiVersion(String apiVersion) {
        this.apiVersion = apiVersion;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public MadBoxiRequestUser getUser() {
        return user;
    }

    public void setUser(MadBoxiRequestUser user) {
        this.user = user;
    }

    public MadBoxiRequestDevice getDevice() {
        return device;
    }

    public void setDevice(MadBoxiRequestDevice device) {
        this.device = device;
    }

    public MadBoxiRequestApp getApp() {
        return app;
    }

    public void setApp(MadBoxiRequestApp app) {
        this.app = app;
    }

    public List<MadBoxiRequestAdSlot> getSlots() {
        return slots;
    }

    public void setSlots(List<MadBoxiRequestAdSlot> slots) {
        this.slots = slots;
    }

    public MadBoxiRequestNetwork getNetwork() {
        return network;
    }

    public void setNetwork(MadBoxiRequestNetwork network) {
        this.network = network;
    }

    public MadBoxiRequestGeo getGeo() {
        return geo;
    }

    public void setGeo(MadBoxiRequestGeo geo) {
        this.geo = geo;
    }
}
