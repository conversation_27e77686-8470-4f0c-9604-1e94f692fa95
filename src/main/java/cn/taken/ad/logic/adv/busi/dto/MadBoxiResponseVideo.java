package cn.taken.ad.logic.adv.busi.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * MADBOXI视频信息
 */
public class MadBoxiResponseVideo implements Serializable {
    private static final long serialVersionUID = -831049895855023137L;
    
    /**
     * 视频URL
     */
    private String video_url;

    private String cover_url;

    
    /**
     * 视频时长（秒）
     */
    private Integer duration;
    
    /**
     * 封面图片宽度，单位象素
     */
    private Integer cover_width;

    /**
     * 封面图片高度，单位象素
     */
    private Integer cover_height;
    
    /**
     * 视频分辨率
     */
    private List<MadBoxiResponseEventImp> event_imp;

    public String getVideo_url() {
        return video_url;
    }

    public void setVideo_url(String video_url) {
        this.video_url = video_url;
    }

    public String getCover_url() {
        return cover_url;
    }

    public void setCover_url(String cover_url) {
        this.cover_url = cover_url;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getCover_width() {
        return cover_width;
    }

    public void setCover_width(Integer cover_width) {
        this.cover_width = cover_width;
    }

    public Integer getCover_height() {
        return cover_height;
    }

    public void setCover_height(Integer cover_height) {
        this.cover_height = cover_height;
    }

    public List<MadBoxiResponseEventImp> getEvent_imp() {
        return event_imp;
    }

    public void setEvent_imp(List<MadBoxiResponseEventImp> event_imp) {
        this.event_imp = event_imp;
    }
}
