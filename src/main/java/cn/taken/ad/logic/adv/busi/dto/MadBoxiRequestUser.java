package cn.taken.ad.logic.adv.busi.dto;

import java.io.Serializable;
import java.util.List;

/**
 * MADBOXI用户信息
 */
public class MadBoxiRequestUser implements Serializable {
    private static final long serialVersionUID = -7064608428216363188L;
    
    /**
     * 用户ID
     */
    private String uid;
    
    /**
     * 年龄
     */
    private Integer age;
    
    /**
     * 性别 0:未知 1:男 2:女
     */
    private Integer gender;
    
    /**
     * 关键词
     */
    private String keywords;
    
    /**
     * 已安装应用列表
     */
    private List<String> appList;
    
    /**
     * 安装应用ID列表
     */
    private List<String> instAids;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public List<String> getAppList() {
        return appList;
    }

    public void setAppList(List<String> appList) {
        this.appList = appList;
    }

    public List<String> getInstAids() {
        return instAids;
    }

    public void setInstAids(List<String> instAids) {
        this.instAids = instAids;
    }
}
