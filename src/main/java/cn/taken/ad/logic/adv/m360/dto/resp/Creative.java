package cn.taken.ad.logic.adv.m360.dto.resp;

import java.util.List;
import java.util.Map;

public class Creative {

    private Integer banner_id; //Y 创意id<br>目前外部DSP创意全部默认为0
    private Integer adspace_slot_seq; //Y 广告位槽位id，如果广告位请求多条创意，360服务器会对返回的多条创意进行编号，从1开始<br>目前有bug，推荐直接使用object在list中的编号
    private String interaction_type; //Y（图片素材或原生素材） 交互类型<br>{<br>ANY = 0; //任何一种<br>NO_INTERACTION = 1; //不交互<br>BROWSE= 2; //浏览<br>DOWNLOAD = 3; //下载<br>DIALING = 4; //电话<br>MESSAGE = 5; //短信<br>MAIL = 6; //邮件<br>}<br>目前仅支持2,3,4
    private String open_type; //Y 落地页打开类型<br>{<br>ALL = 0; //内开、外开由媒体决定<br>INNER = 1; //内开，由媒体WebView打开<br>OUTER = 2; //外开，由浏览器打开<br>}<br>仅针对浏览的交互类型<br>目前仅返回单个值，内开或者外开
    private InteractionObject interaction_object; //Y（图片素材或原生素材） InteractionObject对象，用于描述与用户交互动作，跟interaction_type相对应
    private String package_name; // 广告的APP的包名<br>浏览类广告无，下载类广告部分有
    private Integer package_size; // 广告的APP的大小<br>浏览类广告无，下载类广告部分有
    private String package_md5; // 广告的APP的MD5值<br>浏览类广告无，下载类广告部分有
    private String helper_package_name; // 广告在360手助上的APP包名<br>浏览类广告无，下载类广告部分有
    private String app_name; // 广告的APP名称<br>浏览类广告无，下载类广告部分有
    private Integer version_code; // 广告的APP的versionCode<br>浏览类广告无，下载类广告部分有
    private String version_name; // 广告的APP的版本号，是versionName<br>浏览类广告不填，下载类广告选填
    private String adm_type; //Y 广告素材类型<br>{<br>PIC = 0; //图片素材<br>HTML = 1; //HTML素材<br>NATIVE = 3; //原生素材<br>}<br>目前仅支持原生素材
    private Map<String, Object> adm; //Y 广告素材对象
    private String valid_date; //Y（当且仅当广告位类型为开屏联动） 广告素材的有效日期（有效时间为1天），格式为年-月-日，参见示例
    private List<EventTrack> event_track; //Y EventTrack对象列表，用于各种事件的追踪
    private ComponentInfo component_info; //N ComponentInfo对象，用于描述创意组件信息，ComponentInfo的详细组件定义参考2.2.14 ComponentInfo。含有组件的创意、组件的追踪url中的宏__COMPONENT_TYPE__需要替换后发送，宏替换规则参考含有组件的创意、组件监测说明
    private String app_publisher; // 下载类广告的开发者名称<br>浏览类广告无，下载类广告部分有
    private String app_user_rights; // 下载类广告用户权限(URL)<br>浏览类广告无，下载类广告部分有
    private String app_privacy_agreement; // 下载类广告隐私协议(URL)<br>浏览类广告无，下载类广告部分有


    public Integer getBanner_id() {
        return banner_id;
    }

    public void setBanner_id(Integer banner_id) {
        this.banner_id = banner_id;
    }

    public Integer getAdspace_slot_seq() {
        return adspace_slot_seq;
    }

    public void setAdspace_slot_seq(Integer adspace_slot_seq) {
        this.adspace_slot_seq = adspace_slot_seq;
    }

    public String getInteraction_type() {
        return interaction_type;
    }

    public void setInteraction_type(String interaction_type) {
        this.interaction_type = interaction_type;
    }

    public String getOpen_type() {
        return open_type;
    }

    public void setOpen_type(String open_type) {
        this.open_type = open_type;
    }

    public InteractionObject getInteraction_object() {
        return interaction_object;
    }

    public void setInteraction_object(InteractionObject interaction_object) {
        this.interaction_object = interaction_object;
    }

    public String getPackage_name() {
        return package_name;
    }

    public void setPackage_name(String package_name) {
        this.package_name = package_name;
    }

    public Integer getPackage_size() {
        return package_size;
    }

    public void setPackage_size(Integer package_size) {
        this.package_size = package_size;
    }

    public String getPackage_md5() {
        return package_md5;
    }

    public void setPackage_md5(String package_md5) {
        this.package_md5 = package_md5;
    }

    public String getHelper_package_name() {
        return helper_package_name;
    }

    public void setHelper_package_name(String helper_package_name) {
        this.helper_package_name = helper_package_name;
    }

    public String getApp_name() {
        return app_name;
    }

    public void setApp_name(String app_name) {
        this.app_name = app_name;
    }

    public Integer getVersion_code() {
        return version_code;
    }

    public void setVersion_code(Integer version_code) {
        this.version_code = version_code;
    }

    public String getVersion_name() {
        return version_name;
    }

    public void setVersion_name(String version_name) {
        this.version_name = version_name;
    }

    public String getAdm_type() {
        return adm_type;
    }

    public void setAdm_type(String adm_type) {
        this.adm_type = adm_type;
    }

    public String getValid_date() {
        return valid_date;
    }

    public void setValid_date(String valid_date) {
        this.valid_date = valid_date;
    }

    public List<EventTrack> getEvent_track() {
        return event_track;
    }

    public void setEvent_track(List<EventTrack> event_track) {
        this.event_track = event_track;
    }

    public ComponentInfo getComponent_info() {
        return component_info;
    }

    public void setComponent_info(ComponentInfo component_info) {
        this.component_info = component_info;
    }

    public String getApp_publisher() {
        return app_publisher;
    }

    public void setApp_publisher(String app_publisher) {
        this.app_publisher = app_publisher;
    }

    public String getApp_user_rights() {
        return app_user_rights;
    }

    public void setApp_user_rights(String app_user_rights) {
        this.app_user_rights = app_user_rights;
    }

    public String getApp_privacy_agreement() {
        return app_privacy_agreement;
    }

    public void setApp_privacy_agreement(String app_privacy_agreement) {
        this.app_privacy_agreement = app_privacy_agreement;
    }

    public Map<String, Object> getAdm() {
        return adm;
    }

    public void setAdm(Map<String, Object> adm) {
        this.adm = adm;
    }
}
