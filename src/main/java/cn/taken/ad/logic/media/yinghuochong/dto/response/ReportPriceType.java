package cn.taken.ad.logic.media.yinghuochong.dto.response;

public enum ReportPriceType {
    UNKNOWN_PRICING_TYPE(0),
    CPC(1),
    CPM(2);

    private final int code;

    ReportPriceType(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static ReportPriceType findByCode(int code) {
        for (ReportPriceType type : ReportPriceType.values()) {
            if (type.code == code) {
                return type;
            }
        }
        return CPM; // 默认返回CPM
    }
}
