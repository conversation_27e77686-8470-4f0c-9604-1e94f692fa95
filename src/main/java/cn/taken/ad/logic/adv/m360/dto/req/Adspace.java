package cn.taken.ad.logic.adv.m360.dto.req;

import java.util.List;

public class Adspace {
    private String adspace_id; //Y 广告位id，唯一标识一个广告位，由360移动APP广告平台提供
    private Integer adspace_type; //Y 广告位类型<br>{<br>BANNER = 1; //横幅<br>INTERSTITIAL = 2; //插屏<br>VIDEO = 3; //视频<br>NATIVE = 4; //信息流<br>EMBEDDED = 5; //嵌入式<br>OPENING = 6; //开屏<br>FOCUS = 7; //焦点图<br>OPENING_LINK = 8; // 开屏联动<br>}<br>广告在页面上的展现形式，由媒体按照自己的定义真实填写
    private Integer adspace_position; //Y 广告位位置<br>{<br>NONE = 0; //未知<br>FIRST_POS = 1; //首屏<br>OTHERS = 2; //非首屏<br>}<br>广告在页面上的展现位置，由媒体按照自己的定义真实填写
    private Boolean allowed_html; //Y 由360渲染还是由媒体渲染<br>360渲染（HTML素材）：true<br>媒体渲染（图片素材或者原生素材）：false<br>true和false字母须全部小写<br>目前仅支持媒体渲染，请填false
    private Integer width; //Y（当且仅当广告位类型为横幅，插屏，开屏） 广告位宽度，以像素为单位，与屏幕密度无关
    private Integer height; //Y（当且仅当广告位类型为横幅，插屏，开屏） 广告位高度，以像素为单位，与屏幕密度无关
    private Integer impression_num; // 当前广告位一次请求返回的创意个数，最多支持5个，目前该属性会根据媒体需求在广告位上进行设置，故无需填写该字段，如果要填写，应与双方约定值一致
    private String keywords; // 广告位所在页面的关键词信息，多个关键词使用英文逗号分隔（不能有空格）
    private String channel; // 广告位所在的频道页
    private List<Integer> native_style; // 媒体渲染的广告位支持的原生素材样式<br>若媒体不填，则以360SSP平台的配置为准<br>该字段决定BidResponse.Ads.Creative.Adm.Native结构的字段组合<br>{<br>SINGLE_IMAGE = 1; // 单图样式，MAX会返回img、title、desc、logo<br>LINKED_IMAGE = 2; // 联动双图样式，MAX会返回img、linked_img、title、desc、logo<br>MULTI_IMAGES = 3; // 多图样式，MAX会返回multi_imgs、title、desc、logo<br>VIDEO = 4; // 视频样式，MAX会返回video、img、title、desc、logo<br>}

    public String getAdspace_id() {
        return adspace_id;
    }

    public void setAdspace_id(String adspace_id) {
        this.adspace_id = adspace_id;
    }

    public Integer getAdspace_type() {
        return adspace_type;
    }

    public void setAdspace_type(Integer adspace_type) {
        this.adspace_type = adspace_type;
    }

    public Integer getAdspace_position() {
        return adspace_position;
    }

    public void setAdspace_position(Integer adspace_position) {
        this.adspace_position = adspace_position;
    }

    public Boolean getAllowed_html() {
        return allowed_html;
    }

    public void setAllowed_html(Boolean allowed_html) {
        this.allowed_html = allowed_html;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public Integer getImpression_num() {
        return impression_num;
    }

    public void setImpression_num(Integer impression_num) {
        this.impression_num = impression_num;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public List<Integer> getNative_style() {
        return native_style;
    }

    public void setNative_style(List<Integer> native_style) {
        this.native_style = native_style;
    }
}
