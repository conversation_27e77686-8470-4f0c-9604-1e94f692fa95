package cn.taken.ad.logic.adv.ainian.dto.req;

public class App {

    private Long mainId; //广告主ID(由商务分配)
    private String appId; //应用ID，需要从平台申请(由商务分配)
    private String name; //应用名称
    private String pkg; //平台定义的应用唯一标示（包名）
    private String ver; //应用版本
    private Boolean deepLink; //true 支持;false 不支持;默认支持
    private Integer secure; //是否返回全站https，0 http ;1 https;
    private Boolean gdt; //广点通广告，true 支持；false 不支持； 默认不支持

    public Long getMainId() {
        return mainId;
    }

    public void setMainId(Long mainId) {
        this.mainId = mainId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPkg() {
        return pkg;
    }

    public void setPkg(String pkg) {
        this.pkg = pkg;
    }

    public String getVer() {
        return ver;
    }

    public void setVer(String ver) {
        this.ver = ver;
    }

    public Boolean getDeepLink() {
        return deepLink;
    }

    public void setDeepLink(Boolean deepLink) {
        this.deepLink = deepLink;
    }

    public Integer getSecure() {
        return secure;
    }

    public void setSecure(Integer secure) {
        this.secure = secure;
    }

    public Boolean getGdt() {
        return gdt;
    }

    public void setGdt(Boolean gdt) {
        this.gdt = gdt;
    }
}
