package cn.taken.ad.logic.media.yinghuochong.dto.request;

public enum YingHuoChongReportPriceType {
    UNKNOWN_PRICING_TYPE(0),
    CPC(1),
    CPM(2);

    private final int code;

    YingHuoChongReportPriceType(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static YingHuoChongReportPriceType findByCode(int code) {
        for (YingHuoChongReportPriceType type : YingHuoChongReportPriceType.values()) {
            if (type.code == code) {
                return type;
            }
        }
        return CPM; // 默认返回CPM
    }
}
