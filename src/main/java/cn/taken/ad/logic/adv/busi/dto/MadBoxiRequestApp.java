package cn.taken.ad.logic.adv.busi.dto;

import java.io.Serializable;

/**
 * MADBOXI应用信息
 */
public class MadBoxiRequestApp implements Serializable {
    private static final long serialVersionUID = -7064608428216363191L;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 应用包名
     */
    private String appPackage;
    
    /**
     * 应用名称
     */
    private String appName;
    
    /**
     * 应用版本
     */
    private String appVersion;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppPackage() {
        return appPackage;
    }

    public void setAppPackage(String appPackage) {
        this.appPackage = appPackage;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }
}
