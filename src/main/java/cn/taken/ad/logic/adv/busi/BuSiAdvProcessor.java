package cn.taken.ad.logic.adv.busi;

import cn.taken.ad.component.http.apache.FastHttpClient;
import cn.taken.ad.component.http.apache.HttpResult;
import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.encryption.Md5;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.constant.business.*;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.logic.AdvProcessor;
import cn.taken.ad.logic.adv.busi.dto.*;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbReqAdvBillParam;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.*;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.*;
import cn.taken.ad.utils.ParamParser;
import cn.taken.ad.utils.time.TimeUtils;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * MADBOXI V3.4
 */
@Component("MADBOXI" + LogicSuffix.ADV_LOGIC_SUFFIX)
public class BuSiAdvProcessor implements AdvProcessor {

    public static final String API_VERSION = "apiVersion";

    private static final Logger log = LoggerFactory.getLogger(BuSiAdvProcessor.class);

    @Override
    public RtbResponseDto reqAdv(FastHttpClient httpClient, RtbRequestDto rtb, RtbAdvDto advDto) throws Throwable {
        DiShuYunPaiAdvRequest request = convertRequest(rtb, advDto);
        String json = JsonHelper.toJsonStringWithoutNull(request);
        advDto.setReqObj(json);
        HttpResult httpResult = httpClient.postJson(advDto.getRtburl(), json, "UTF-8", new Header[]{new BasicHeader("Content-Type", "application/json;charset=UTF-8")},advDto.getTimeout());
        RtbResponseDto fail = isHttpRequestFail(httpResult);
        if (fail != null) {
            advDto.setRespObj(fail);
            return fail;
        }
        String resp = httpResult.getDataStringUTF8();
        advDto.setRespObj(resp);
        if (StringUtils.isEmpty(resp)){
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), "Resp empty");
        }
        //log.info("RtbId:{} Req Adv:{},MediaChAppId:{},AdvChAppId:{},MediaChTagId:{},AdvChTagId:{},Request:{}, Resp:{} ", rtbDto.getRtbId(), rtbDto.getAdvAccountId(), rtbDto.getMediaChAppId(), rtbDto.getAdvChAppId(), rtbDto.getMediaChTagId(), rtbDto.getAdvChTagId(), json, resp);
        return parseResponse(rtb,request, resp);
    }

    @Override
    public SuperResult<String> billResult(FastHttpClient httpClient, RtbReqAdvBillParam reqDto) {
        // 是否有请求成功的
        boolean hasRight = false;
        List<String> urls = reqDto.getUrls();
        Double price = reqDto.getPrice();
        if (!reqDto.getBiddingSuccess()){
            // 竟败
            if (null != price){
                urls = replaceMacro("__WIN_PRICE__",urls,price.toString());
            }
        }
        for (String url : urls) {
            HttpResult result = httpClient.get(url, null, null, 10000);
            if (!hasRight) {
                hasRight = result.isSuccess();
            }
            //log.info("Req Adv Bill ReqId:{}, Result:{},HttpCode:{},Header:{},ErrMsg:{},url:{}", reqDto.getRtbId(), result.isSuccess(), result.getStatusLine(), Arrays.toString(result.getHeaders()), null != result.getThrowable() ? result.getThrowable().getMessage() : "", url);
        }
        return hasRight ? SuperResult.rightResult() : SuperResult.badResult("no right");
    }

    public RtbResponseDto parseResponse(RtbRequestDto rtbDto, DiShuYunPaiAdvRequest request, String resp) throws Exception {
        DiShYunResponse response;
        try {
            response = JsonHelper.fromJson(DiShYunResponse.class, resp);
        } catch (Exception e) {
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), "Resp convert fail");
        }
        if(null == response.getCode()){
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), "no resp code");
        }
        if (20000 != response.getCode()) {
            if (20001 == response.getCode() || 20002 == response.getCode()) {
                return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
            }else{
                return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), response.getMsg(),response.getCode()+"");
            }
        }
        if (null == response.getAds() || response.getAds().isEmpty()) {
            return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
        }
        RtbResponseDto responseDto = new RtbResponseDto();
        responseDto.setCode(LogicState.SUCCESS.getCode());
        response.getAds().forEach(respTag -> {
            TagResponseDto tagResponseDto = new TagResponseDto();
            // adId 广告ID
            if (null != respTag.getPrice()){
                tagResponseDto.setPrice(respTag.getPrice().doubleValue());
            }
            DiShYunResponseMaterialMeta materialMeta = respTag.getCreative();
            if(null != materialMeta.getAdLogo() && StringUtils.isNotEmpty(materialMeta.getAdLogo().getUrl())){
                tagResponseDto.setLogoUrl(materialMeta.getAdLogo().getUrl());
            }
            if(null != materialMeta.getAdText() && StringUtils.isNotEmpty(materialMeta.getAdText().getUrl())){
                tagResponseDto.setIconUrl(materialMeta.getAdText().getUrl());
            }
            // adType
            tagResponseDto.setTitle(materialMeta.getTitle());
            tagResponseDto.setDesc(materialMeta.getDescription());
            if (null != materialMeta.getLogo() && StringUtils.isNotEmpty(materialMeta.getLogo().getUrl())){
                tagResponseDto.setLogoUrl(materialMeta.getLogo().getUrl());
            }
            if (StringUtils.isNotEmpty(materialMeta.getIcon())){
                tagResponseDto.setIconUrl(materialMeta.getIcon());
            }

            if (null != materialMeta.getCreativeType()){
                switch (materialMeta.getCreativeType()){
                    case 1:
                        tagResponseDto.setMaterialType(MaterialType.TEXT);
                        break;
                    case 2:
                    case 3:
                        tagResponseDto.setMaterialType(MaterialType.IMAGE_TEXT);
                        break;
                    case 4:
                        tagResponseDto.setMaterialType(MaterialType.VIDEO);
                        break;
                    case 5:
                        tagResponseDto.setMaterialType(MaterialType.HTML);
                        break;
                }
            }
            tagResponseDto.setImgUrls(new ArrayList<>());
            OrientationType orientationType = null;
            if (null != materialMeta.getImageMode()){
                switch (materialMeta.getImageMode()){
                    case 2:
                    case 3:
                        if (null != materialMeta.getImage() && StringUtils.isNotEmpty(materialMeta.getImage().getUrl())){
                            if (null == tagResponseDto.getMaterialType()){
                                tagResponseDto.setMaterialType(MaterialType.IMAGE_TEXT);
                            }
                            tagResponseDto.getImgUrls().add(materialMeta.getImage().getUrl());
                            tagResponseDto.setMaterialWidth(materialMeta.getImage().getWidth());
                            tagResponseDto.setMaterialHeight(materialMeta.getImage().getHeight());
                        }
                        break;
                    case 4:
                        if (null != materialMeta.getImageList() && !materialMeta.getImageList().isEmpty()){
                            if (null == tagResponseDto.getMaterialType()){
                                tagResponseDto.setMaterialType(MaterialType.IMAGE_TEXT);
                            }
                            materialMeta.getImageList().forEach(item ->{
                                if (StringUtils.isNotEmpty(item.getUrl())){
                                    tagResponseDto.getImgUrls().add(item.getUrl());
                                }
                            });
                        }
                    case 5:
                        orientationType = OrientationType.HORIZONTAL;
                    case 15:
                        if (null != materialMeta.getVideo()){
                            if (null == tagResponseDto.getMaterialType()){
                                tagResponseDto.setMaterialType(MaterialType.VIDEO);
                            }
                            DiShYunResponseVideo responseVideo = materialMeta.getVideo();
                            ResponseVideoDto videoDto = new ResponseVideoDto();
                            videoDto.setVideoUrl(responseVideo.getVideoUrl());
                            if (null != responseVideo.getVideoDuration()){
                                videoDto.setDuration(responseVideo.getVideoDuration().intValue());
                            }
                            if (null != responseVideo.getSize()){
                                videoDto.setVideoSize(responseVideo.getSize().longValue());
                            }
                            videoDto.setResolution(responseVideo.getResolution());
                            if (StringUtils.isNotEmpty(responseVideo.getCoverUrl())){
                                videoDto.setCoverImgUrls(Collections.singletonList(responseVideo.getCoverUrl()));
                            }
                            videoDto.setCoverWidth(responseVideo.getCoverWidth());
                            videoDto.setCoverHeight(responseVideo.getCoverHeight());

                            if (null != materialMeta.getAdRewardInfo()){
                                DiShYunResponseRewardInfo rewardInfo = materialMeta.getAdRewardInfo();
                                videoDto.setSkipSeconds(rewardInfo.getSkipShowTime());
                                if (null != rewardInfo.getRewardTime()){
                                    videoDto.setDuration(rewardInfo.getRewardTime());
                                }
                                if (null != rewardInfo.getShowLandingPage() && rewardInfo.getShowLandingPage() == 1){
                                    videoDto.setClickAble(true);
                                }
                            }
                            if (null == orientationType){
                                orientationType = OrientationType.VERTICAL;
                            }
                            videoDto.setOrientationType(orientationType.getType());
                            tagResponseDto.setVideoInfo(videoDto);
                        }
                }
            }
            tagResponseDto.setActionType(ActionType.WEB_VIEW_H5);
            tagResponseDto.setClickUrl(materialMeta.getTargetUrl());

            if(null != materialMeta.getInteractionType()){
                switch (materialMeta.getInteractionType()){
                    case 2:
                        tagResponseDto.setActionType(ActionType.SYSTEM_BROWSER_H5);
                        break;
                    case 3:
                        tagResponseDto.setActionType(ActionType.WEB_VIEW_H5);
                        break;
                    case 4:
                        tagResponseDto.setActionType(ActionType.DOWNLOAD);
                        tagResponseDto.setClickUrl(materialMeta.getDownloadUrl());
                        break;
                    case 5:
                        tagResponseDto.setActionType(ActionType.DEEPLINK);
                        break;
                    case 6:
                        tagResponseDto.setActionType(ActionType.MINI_PROGRAM);
                        ResponseMiniProgramDto responseMiniProgramDto = new ResponseMiniProgramDto();
                        if (StringUtils.isNotEmpty(materialMeta.getWechatAppId()) && StringUtils.isNotBlank(materialMeta.getWechatAppPath())){
                            responseMiniProgramDto.setUserName(materialMeta.getWechatAppId());
                            responseMiniProgramDto.setPath(materialMeta.getWechatAppPath());
                        }
                        tagResponseDto.setMiniProgram(responseMiniProgramDto);
                        break;
                }
            }
            tagResponseDto.setDeepLinkUrl(materialMeta.getDeeplink());
            tagResponseDto.setUniversalLink(materialMeta.getUniversalLink());
            if (null != materialMeta.getInteractionType() && materialMeta.getInteractionType()==4){
                ResponseAppDto appDto = new ResponseAppDto();
                appDto.setAppName(materialMeta.getAppName());
                appDto.setPackageName(materialMeta.getAppPackage());
                appDto.setAppIconUrl(materialMeta.getAppIconUrl());
                if (StringUtils.isNotEmpty(materialMeta.getAppSize())){
                    long size = Long.parseLong(materialMeta.getAppSize());
                    appDto.setAppSize(size * 1024 * 1024);
                }
                appDto.setAppDeveloper(materialMeta.getAppDeveloper());
                appDto.setAppVersion(materialMeta.getAppVersionName());
                appDto.setAppInfo(materialMeta.getAppDescription());
                appDto.setAppPermissionInfoUrl(materialMeta.getAppPermissionUrl());
                appDto.setAppPrivacyUrl(materialMeta.getAppPrivacyPolicyUrl());
                tagResponseDto.setAppInfo(appDto);
            }
            if (null != materialMeta.getCreativeType() && materialMeta.getCreativeType()==5){
                tagResponseDto.setHtmlContent(materialMeta.getHtmlSnippet());
                tagResponseDto.setMaterialHeight(materialMeta.getHtmlHeight());
                tagResponseDto.setMaterialWidth(materialMeta.getHtmlWidth());
            }
            List<ResponseTrackDto> tracks = new ArrayList<>();
            tagResponseDto.setTracks(tracks);
            if (null != materialMeta.getWinNoticeUrl() && !materialMeta.getWinNoticeUrl().isEmpty()){
                tagResponseDto.setWinNoticeUrls(materialMeta.getWinNoticeUrl());
            }
            if (null != materialMeta.getLossNoticeUrl() && !materialMeta.getLossNoticeUrl().isEmpty()){
                tagResponseDto.setFailNoticeUrls(materialMeta.getLossNoticeUrl());
            }
            if (null != materialMeta.getShowUrl() && !materialMeta.getShowUrl().isEmpty()){
                tracks.add(new ResponseTrackDto(EventType.EXPOSURE.getType(), materialMeta.getShowUrl()));
            }
            if (null != materialMeta.getClickUrl() && !materialMeta.getClickUrl().isEmpty()){
                tracks.add(new ResponseTrackDto(EventType.CLICK.getType(), materialMeta.getClickUrl()));
            }
            if (null != materialMeta.getCloseUrl() && !materialMeta.getCloseUrl().isEmpty()){
                tracks.add(new ResponseTrackDto(EventType.CLOSE_AD.getType(), materialMeta.getCloseUrl()));
            }
            if (null != materialMeta.getDlStartUrl() && !materialMeta.getDlStartUrl().isEmpty()){
                tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_BEGIN.getType(), materialMeta.getDlStartUrl()));
            }
            if (null != materialMeta.getDlFinishUrl() && !materialMeta.getDlFinishUrl().isEmpty()){
                tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_COMPLETED.getType(), materialMeta.getDlFinishUrl()));
            }
            if (null != materialMeta.getInstStartUrl() && !materialMeta.getInstStartUrl().isEmpty()){
                tracks.add(new ResponseTrackDto(EventType.INSTALL_BEGIN.getType(), materialMeta.getInstStartUrl()));
            }
            if (null != materialMeta.getInstFinishUrl() && !materialMeta.getInstFinishUrl().isEmpty()){
                tracks.add(new ResponseTrackDto(EventType.INSTALL_COMPLETED.getType(), materialMeta.getInstFinishUrl()));
            }
            if (null != materialMeta.getInstOpenUrl() && !materialMeta.getInstOpenUrl().isEmpty()){
                tracks.add(new ResponseTrackDto(EventType.INSTALLED_OPEN.getType(), materialMeta.getInstOpenUrl()));
            }
            if (null != materialMeta.getInstActiveUrl() && !materialMeta.getInstActiveUrl().isEmpty()){
                tracks.add(new ResponseTrackDto(EventType.ACTIVE_APP.getType(), materialMeta.getInstActiveUrl()));
            }
            if (null != materialMeta.getDeeplinkOpenUrl() && !materialMeta.getDeeplinkOpenUrl().isEmpty()){
                tracks.add(new ResponseTrackDto(EventType.DEEPLINK_START.getType(), materialMeta.getDeeplinkOpenUrl()));
            }
            if (null != materialMeta.getDeeplinkSucUrl() && !materialMeta.getDeeplinkSucUrl().isEmpty()){
                tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_SUCCESS.getType(), materialMeta.getDeeplinkSucUrl()));
            }
            if (null != materialMeta.getDeeplinkFailUrl() && !materialMeta.getDeeplinkFailUrl().isEmpty()){
                tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_FAIL.getType(), materialMeta.getDeeplinkFailUrl()));
            }
            if (null != materialMeta.getVideoLoadUrl() && !materialMeta.getVideoLoadUrl().isEmpty()){
                tracks.add(new ResponseTrackDto(EventType.VIDEO_LOADED_SUCCESS.getType(), materialMeta.getVideoLoadUrl()));
            }
            if (null != materialMeta.getVideoSkippedUrl() && !materialMeta.getVideoSkippedUrl().isEmpty()){
                tracks.add(new ResponseTrackDto(EventType.VIDEO_SKIP.getType(), materialMeta.getVideoSkippedUrl()));
            }
            if (null != materialMeta.getVideoClosedUrl() && !materialMeta.getVideoClosedUrl().isEmpty()){
                tracks.add(new ResponseTrackDto(EventType.VIDEO_CLOSE.getType(), materialMeta.getVideoClosedUrl()));
            }
            if (null != materialMeta.getPlayStartUrl() && !materialMeta.getPlayStartUrl().isEmpty()){
                tracks.add(new ResponseTrackDto(EventType.VIDEO_BEGIN.getType(), materialMeta.getPlayStartUrl()));
            }
            // 暂无 playInteruptUrl 视频中断
            if (null != materialMeta.getPlayCompleteUrl() && !materialMeta.getPlayCompleteUrl().isEmpty()){
                tracks.add(new ResponseTrackDto(EventType.VIDEO_END.getType(), materialMeta.getPlayCompleteUrl()));
            }
            if (null != materialMeta.getFullScreenPlayUrl() && !materialMeta.getFullScreenPlayUrl().isEmpty()){
                tracks.add(new ResponseTrackDto(EventType.VIDEO_FULL_SCREEN.getType(), materialMeta.getFullScreenPlayUrl()));
            }
            // 暂无 previewPlayUrl 信息流视频广告点击预览图播放
            if (null != respTag.getTracks() && !respTag.getTracks().isEmpty()) {
                respTag.getTracks().forEach(respTrack -> {
                    if (null != respTrack.getType() && respTrack.getUrls() != null && !respTrack.getUrls().isEmpty()) {
                        switch (respTrack.getType()) {
                            case 0:
                                tracks.add(new ResponseTrackDto(EventType.VIDEO_BEGIN.getType(), respTrack.getUrls()));
                                break;
                            case 1:
                                tracks.add(new ResponseTrackDto(EventType.VIDEO_25.getType(), respTrack.getUrls()));
                                break;
                            case 2:
                                tracks.add(new ResponseTrackDto(EventType.VIDEO_50.getType(), respTrack.getUrls()));
                                break;
                            case 3:
                                tracks.add(new ResponseTrackDto(EventType.VIDEO_75.getType(), respTrack.getUrls()));
                                break;
                            case 4:
                                tracks.add(new ResponseTrackDto(EventType.VIDEO_END.getType(), respTrack.getUrls()));
                                break;
                            case 101:
                                tracks.add(new ResponseTrackDto(EventType.VIDEO_FULL_SCREEN.getType(), respTrack.getUrls()));
                                break;
                            case 102:
                                tracks.add(new ResponseTrackDto(EventType.VIDEO_EXITS_FULL_SCREEN.getType(), respTrack.getUrls()));
                                break;
                            case 103:
                                tracks.add(new ResponseTrackDto(EventType.VIDEO_MUTE.getType(), respTrack.getUrls()));
                                break;
                            case 104:
                                tracks.add(new ResponseTrackDto(EventType.VIDEO_UNMUTE.getType(), respTrack.getUrls()));
                                break;
                            case 105:
                                tracks.add(new ResponseTrackDto(EventType.VIDEO_SLIDE_UP.getType(), respTrack.getUrls()));
                                break;
                            case 106:
                                tracks.add(new ResponseTrackDto(EventType.VIDEO_SLIDE_DOWN.getType(), respTrack.getUrls()));
                                break;
                            case 107:
                                tracks.add(new ResponseTrackDto(EventType.VIDEO_PAUSE.getType(), respTrack.getUrls()));
                                break;
                            case 108:
                                tracks.add(new ResponseTrackDto(EventType.VIDEO_CONTINUE.getType(), respTrack.getUrls()));
                                break;
                            case 109:
                                tracks.add(new ResponseTrackDto(EventType.VIDEO_PLAY_REPLAY.getType(), respTrack.getUrls()));
                                break;
                            case 110:
                                tracks.add(new ResponseTrackDto(EventType.VIDEO_PLAY_ERROR.getType(), respTrack.getUrls()));
                                break;
                        }
                    }
                });
            }
            // 宏替换
            tagResponseDto.getTracks().forEach(track ->{
                List<String> urls = track.getTrackUrls();
                // __WIDTH__ __HEIGHT__ 与平台一致
                urls = replaceMacro("__IMP_X1__",urls, MacroType.DISPLAY_LUX.getCode());
                urls = replaceMacro("__IMP_Y1__",urls, MacroType.DISPLAY_LUY.getCode());

                urls = replaceMacro("__IMP_X2__",urls, MacroType.DISPLAY_RDX.getCode());
                urls = replaceMacro("__IMP_Y2__",urls, MacroType.DISPLAY_RDY.getCode());

                urls = replaceMacro("__BTN_X1__",urls, MacroType.BUTTON_LUX.getCode());
                urls = replaceMacro("__BTN_Y1__",urls, MacroType.BUTTON_LUY.getCode());

                urls = replaceMacro("__BTN_X2__",urls, MacroType.BUTTON_RDX.getCode());
                urls = replaceMacro("__BTN_Y2__",urls, MacroType.BUTTON_RDY.getCode());
                // __DOWN_X__  __DOWN_Y__ __UP_X__ __UP_Y__ __DP_WIDTH__ __DP_HEIGHT__ __DP_DOWN_X__ __DP_DOWN_Y__ __DP_UP_X__ __DP_UP_Y__与平台一致

                urls = replaceMacro("__CURR_TIME__",urls, MacroType.TIME_SECONDS.getCode());
                urls = replaceMacro("__ORIGINTIME__",urls, MacroType.START_TIME.getCode());
                urls = replaceMacro("__EVENT_TIME_START__",urls, MacroType.START_TIME.getCode());
                urls = replaceMacro("__EVENT_TIME_END__",urls, MacroType.END_TIME.getCode());

                urls = replaceMacro("__LATITUDE__",urls, MacroType.LAT.getCode());
                urls = replaceMacro("__LONGITUDE__",urls, MacroType.LON.getCode());
                // __CUSTOMER_DESC__ 自定义宏：不上报则不需要进行宏替换
                urls = replaceMacro("__VIDEO_PLAYTIME__",urls, MacroType.VIDEO_PROGRESS_SEC.getCode());
                urls = replaceMacro("__VIDEO_PLAYTIMEMS__",urls, MacroType.VIDEO_PROGRESS.getCode());
                urls = replaceMacro("__PLAY_BEGINTIME__",urls, MacroType.VIDEO_BEGIN_TIME.getCode());
                urls = replaceMacro("__PLAY_ENDTIME__",urls, MacroType.VIDEO_END_TIME.getCode());
                urls = replaceMacro("__PLAY_FIRST_FRAME__",urls, MacroType.VIDEO_PLAY_FIRST_FRAME.getCode());
                urls = replaceMacro("__PLAY_LAST_FRAME__",urls, MacroType.VIDEO_PLAY_LAST_FRAME.getCode());
                urls = replaceMacro("__PLAY_MODE__",urls, MacroType.VIDEO_BEHAVIOR.getCode());
                urls = replaceMacro("__PLAY_SCENE__",urls, MacroType.VIDEO_PLAY_LAST_FRAME.getCode());
                urls = replaceMacro("__PLAY_STATUS__",urls, MacroType.VIDEO_STATUS.getCode());
                urls = replaceMacro("__PLAY_TYPE__",urls, MacroType.VIDEO_TYPE.getCode());
                urls = replaceMacro("__TG_APP_INST__",urls, MacroType.TARGET_APP_INSTALL.getCode());
                track.setTrackUrls(urls);
            });
            responseDto.getTags().add(tagResponseDto);
        });
        return responseDto;
    }


    /**
     * 参数转换为广告主需要的格式
     */
    private DiShuYunPaiAdvRequest convertRequest(RtbRequestDto rtbDto,  RtbAdvDto advDto){
        DiShuYunPaiAdvRequest request = new DiShuYunPaiAdvRequest();
        String apiVersion = ParamParser.parseParamByJson(advDto.getPnyParam()).get(API_VERSION);
        request.setApiVersion(apiVersion);
        request.setRequestId(rtbDto.getReqId());
        RequestDeviceDto deviceInfo = rtbDto.getDevice();
        RequestNetworkDto networkInfo = rtbDto.getNetwork();

        request.setUser(convertUser( rtbDto.getUser(),deviceInfo.getInstalledAppInfo()));
        request.setDevice(convertDevice(deviceInfo,networkInfo));
        request.setApp(convertApp(advDto,rtbDto.getApp()));
        request.setSlots(convertSlot(rtbDto.getTag(),advDto.getTagCode(),rtbDto.getTag().getPrice()));
        request.setNetwork(convertNetwork(networkInfo));
        request.setGeo(convertGeo(rtbDto.getGeo()));
        return request;
    }

    private DiShYunRequestGeo convertGeo(RequestGeoDto info) {
        DiShYunRequestGeo geo = new DiShYunRequestGeo();
        if (null != info){
            if (null != info.getLatitude()){
                geo.setLatitude(info.getLatitude().floatValue());
            }
            if (null != info.getLongitude()){
                geo.setLongitude(info.getLongitude().floatValue());
            }
        }
        return geo;
    }

    private DiShYunRequestNetwork convertNetwork(RequestNetworkDto info) {
        DiShYunRequestNetwork request = new DiShYunRequestNetwork();
        request.setConnectionType(0);
        if (null != info.getConnectType()){
            switch (info.getConnectType()) {
                case WIFI:
                    request.setConnectionType(1);
                    break;
                case NETWORK_2G:
                    request.setConnectionType(2);
                    break;
                case NETWORK_3G:
                    request.setConnectionType(3);
                    break;
                case NETWORK_4G:
                    request.setConnectionType(4);
                    break;
                case NETWORK_5G:
                    request.setConnectionType(5);
                    break;
                case ETHERNET:
                    request.setConnectionType(10);
                    break;
                default:
                    request.setConnectionType(0);
                    break;
            }
        }
        request.setCarrier(0);
        if (null != info.getCarrierType()){
            switch (info.getCarrierType()) {
                case CM:
                    request.setCarrier(1);
                    break;
                case CU:
                    request.setCarrier(2);
                    break;
                case CT:
                    request.setCarrier(3);
                    break;
                default:
                    request.setCarrier(0);
                    break;
            }
        }
        request.setLac("");
        request.setCid("");
        request.setSsid(null != info.getSsid() ? info.getSsid() : "");
        request.setBssid("");
        return request;
    }

    private List<DiShYunRequestAdSlot> convertSlot(RequestTagDto info, String advChTagId, Double price){
        DiShYunRequestAdSlot request = new DiShYunRequestAdSlot();
        request.setSlotId(advChTagId);
        if (null != info.getTagType()) {
            switch (info.getTagType()){
                case BANNER:
                    request.setAdType(1);
                    break;
                case INTERSTITIAL:
                    request.setAdType(2);
                    break;
                case OPEN:
                    request.setAdType(3);
                    break;
                case INFORMATION_FLOW:
                    request.setAdType(5);
                    break;
                case INCENTIVE_VIDEO:
                    request.setAdType(7);
                    break;
                case NATIVE:
                    request.setAdType(9);
                    break;
                default:
                    request.setAdType(0);
                    break;
            }
        }else{
            request.setAdType(0);
        }

        DiShYunRequestSlotSize slotSize = new DiShYunRequestSlotSize();
        if (null != info.getWidth()){
            slotSize.setWidth(info.getWidth());
        }
        if (null != info.getHeight()){
            slotSize.setHeight(info.getHeight());
        }
        request.setSlotSizes(Collections.singletonList(slotSize));
        request.setSlotSizes(new ArrayList<>());
        request.setAdCount(1);
        if (null != info.getMinDuration()){
            request.setMinDuration(info.getMinDuration());
        }
        if (null != info.getMaxDuration()){
            request.setMaxDuration(info.getMaxDuration());
        }
        // 视频格式：0 任意格式、1 flv、2 mp4 暂时传 0
        request.setVideoType(0);
        if (null != price){
            request.setBidPrice(price.intValue());
        }
        // 暂无 secure 是否需要 HTTPS 资源（创意素材和监测链接）
        return Collections.singletonList(request);
    }
    private DiShYunRequestApp convertApp(RtbAdvDto advDto, RequestAppDto info){
        DiShYunRequestApp request = new DiShYunRequestApp();
        request.setAppId(advDto.getAppCode());
        request.setAppPackage(null != info.getBundle() ? info.getBundle() : "");
        if (StringUtils.isNotEmpty(info.getAppName())){
            request.setAppName(info.getAppName());
        }else if (StringUtils.isNotEmpty(advDto.getAppName())){
            request.setAppName(advDto.getAppName());
        }else{
            request.setAppName("");
        }
        request.setAppVersion(null != info.getAppVersion() ? info.getAppVersion() : "");
        return request;
    }

    private DiShYunRequestDevice convertDevice(RequestDeviceDto deviceInfo, RequestNetworkDto networkInfo){
        DiShYunRequestDevice request = new DiShYunRequestDevice();
        request.setImei(null != deviceInfo.getImei() ? deviceInfo.getImei() : "");
        request.setImeiMd5(null != deviceInfo.getImeiMd5() ? deviceInfo.getImeiMd5().toLowerCase() : "");
        request.setIdfa(null != deviceInfo.getIdfa() ? deviceInfo.getIdfa() : "");
        request.setIdfaMd5(null != deviceInfo.getIdfaMd5() ? deviceInfo.getIdfaMd5().toLowerCase() : "");
        request.setOaid(null != deviceInfo.getOaid() ? deviceInfo.getOaid() : "");
        request.setOaidMd5(null != deviceInfo.getOaidMd5() ? deviceInfo.getOaidMd5().toLowerCase() : "");
        request.setVaid("");
        request.setVaidMd5("");
        request.setAndroidId(null != deviceInfo.getAndroidId() ? deviceInfo.getAndroidId() : "");
        request.setAndroidIdMd5(null != deviceInfo.getAndroidIdMd5() ? deviceInfo.getAndroidIdMd5().toLowerCase() : "");
        request.setIdfv(null != deviceInfo.getIdfv() ? deviceInfo.getIdfv() : "");
        request.setMac(null != networkInfo.getMac() ? networkInfo.getMac() : "");
        request.setMacMd5(null != networkInfo.getMacMd5() ? networkInfo.getMacMd5().toLowerCase() : "");
        List<DiShYunRequestCaid> caids = new ArrayList<>();
        if (null != deviceInfo.getCaids() && !deviceInfo.getCaids().isEmpty()){
            deviceInfo.getCaids().forEach(item ->{
                if (StringUtils.isNotEmpty(item.getCaid()) && StringUtils.isNotEmpty(item.getVersion())){
                    caids.add(new DiShYunRequestCaid(item.getCaid(), item.getVersion()));
                }
            });
        }
        request.setCaids(caids);
        request.setImsi(null != deviceInfo.getImsi() ? deviceInfo.getImsi() : "");
        // mccmnc 运营商识别码 暂无
        request.setUa(null != deviceInfo.getUserAgent() ? deviceInfo.getUserAgent() : "");
        request.setIp(null != networkInfo.getIp() ? networkInfo.getIp() : "");
        request.setDevType(0);
        if (null != deviceInfo.getDeviceType()){
            switch (deviceInfo.getDeviceType()){
                case PHONE:
                    request.setDevType(1);
                    break;
                case PAD:
                    request.setDevType(2);
                    break;
                case TV:
                    request.setDevType(3);
                    break;
                case PC:
                    request.setDevType(4);
                    break;
                default:
                    request.setDevType(0);
                    break;
            }
        }
        request.setOs(0);
        if (null != deviceInfo.getOsType()){
            switch (deviceInfo.getOsType()){
                case ANDROID:
                    request.setOs(1);
                    break;
                case IOS:
                    request.setOs(2);
                    break;
                case WINDOWS_PC:
                case WINDOWS_PHONE:
                    request.setOs(3);
                    break;
                case HARMONY:
                    request.setOs(4);
                    break;
                default:
                    request.setOs(0);
                    break;
            }
        }
        request.setOsVersion(null != deviceInfo.getOsVersion() ? deviceInfo.getOsVersion() : "");
        request.setVendor(null != deviceInfo.getVendor() ? deviceInfo.getVendor() : "");
        request.setBrand(null != deviceInfo.getBrand() ? deviceInfo.getBrand() : "");
        request.setModel(null != deviceInfo.getModel() ? deviceInfo.getModel() : "");
        request.setLanguage("其他");
        if (StringUtils.isNotEmpty(deviceInfo.getLanguage())){
            if (deviceInfo.getLanguage().toLowerCase().contains("zh")){
                request.setLanguage("中文");
            }else if (deviceInfo.getLanguage().toLowerCase().contains("en")){
                request.setLanguage("英文");
            }
        }
        request.setOrientation(0);
        if (null != deviceInfo.getOrientation()){
            if (deviceInfo.getOrientation().getType() == 1){
                request.setOrientation(2);
            }else if (deviceInfo.getOrientation().getType() == 2){
                request.setOrientation(1);
            }
        }
        if (null != deviceInfo.getPpi()){
            request.setPpi(deviceInfo.getPpi());
        }
        if (null != deviceInfo.getWidth()){
            request.setScreenWidth(deviceInfo.getWidth());
        }
        if (null != deviceInfo.getHeight()){
            request.setScreenHeight(deviceInfo.getHeight());
        }
        request.setBootMark(null != deviceInfo.getBootMark() ? deviceInfo.getBootMark() : "");
        request.setUpdateMark(null != deviceInfo.getUpdateMark() ? deviceInfo.getUpdateMark() : "");
        request.setCountryCode(null != deviceInfo.getCountry() ? deviceInfo.getCountry() : "");
        request.setTimeZone(null != deviceInfo.getTimeZone() ? deviceInfo.getTimeZone() : "");
        if (StringUtils.isNotEmpty(deviceInfo.getDeviceName())){
            request.setDeviceNameMd5(Md5.md5(deviceInfo.getDeviceName()).toLowerCase());
        }else if (StringUtils.isNotEmpty(deviceInfo.getDeviceNameMd5())){
            request.setDeviceNameMd5(deviceInfo.getDeviceNameMd5().toLowerCase());
        }else{
            request.setDeviceNameMd5("");
        }
        request.setHardwareMachine(null != deviceInfo.getHardwareMachine() ? deviceInfo.getHardwareMachine() : "");
        if (null != deviceInfo.getDeviceHardDisk()){
            request.setDiskTotal(deviceInfo.getDeviceHardDisk());
        }
        if (null != deviceInfo.getDeviceMemory()){
            request.setMemTotal(deviceInfo.getDeviceMemory());
        }
        request.setAppStoreVersion(null != deviceInfo.getAppStoreVersion() ? deviceInfo.getAppStoreVersion() : "");
        request.setHmsCore(null != deviceInfo.getHmsVersion() ? deviceInfo.getHmsVersion() : "");
        request.setMiuiVersion(null != deviceInfo.getSysUiVersion() ? deviceInfo.getSysUiVersion() : "");
        request.setPaid(null != deviceInfo.getPaid() ? deviceInfo.getPaid() : "");
        request.setAaid(null != deviceInfo.getAaid() ? deviceInfo.getAaid() : "");
        Long startTime = TimeUtils.convertMilliSecond(deviceInfo.getSysStartTime());
        if (null != startTime){
            request.setSystemStartTime(startTime.toString());
        }
        request.setSystemUpdateTime(StringUtils.isNotEmpty(deviceInfo.getSysUpdateTime()) ? deviceInfo.getSysUpdateTime() : "");
        request.setDeviceInitTime(null != deviceInfo.getSysInitTime() ? deviceInfo.getSysInitTime() : "");
        return request;
    }

    private DiShYunRequestUser convertUser(RequestUserDto userInfo, List<RequestInstalledAppDto> installApps){
        DiShYunRequestUser requestUser = new DiShYunRequestUser();
        if (null != userInfo){
            requestUser.setUid(null != userInfo.getUserId() ? userInfo.getUserId() : "");
            if (null != userInfo.getAge()){
                requestUser.setAge(userInfo.getAge());
            }
            requestUser.setGender(0);
            if (null != userInfo.getGender()){
                if (userInfo.getGender().equalsIgnoreCase("M")){
                    requestUser.setGender(1);
                }else if (userInfo.getGender().equalsIgnoreCase("F")){
                    requestUser.setGender(2);
                }
            }
            requestUser.setKeywords("");
            if (null != userInfo.getInterest() && userInfo.getInterest().length > 0){
                requestUser.setKeywords(org.apache.commons.lang3.StringUtils.join(userInfo.getInterest(), ","));
            }
        }
        List<String> installApp = new ArrayList<>();
        if (installApps != null && !installApps.isEmpty()){
            installApps.forEach(item ->{
                if (StringUtils.isNotEmpty(item.getPackageName())){
                    installApp.add(item.getPackageName());
                }
            });
        }
        requestUser.setAppList(installApp);
        // instAids 暂无
        return requestUser;
    }
}
