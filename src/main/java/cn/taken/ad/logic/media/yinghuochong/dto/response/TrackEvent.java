package cn.taken.ad.logic.media.yinghuochong.dto.response;

public enum TrackEvent {
    VIDEO_START(0),
    VIDEO_CLOSE(1),
    VIDEO_READY_PLAY(2),
    VIDEO_CONTINUE_PLAY(3),
    VIDEO_PAUSE(4),
    VIDEO_PLAY_END(5),
    VIDEO_REPEATED_PLAY(6),
    SKIP(7),
    VIDEO_PLAY_FAIL(8),
    VIDEO_TURN_ON_OFF_SOUND_BUTTON(9);

    private final int code;

    TrackEvent(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static TrackEvent findByCode(int code) {
        for (TrackEvent event : TrackEvent.values()) {
            if (event.code == code) {
                return event;
            }
        }
        return VIDEO_START; // 默认返回视频开始播放
    }
}
