package cn.taken.ad.logic.media.yinghuochong.dto.request;

public enum YingHuoChongAppType {
    QUICK_APP(1),
    APP(2);

    private final int code;

    YingHuoChongAppType(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static YingHuoChongAppType findByCode(int code) {
        for (YingHuoChongAppType type : YingHuoChongAppType.values()) {
            if (type.code == code) {
                return type;
            }
        }
        return APP; // 默认返回普通APP
    }
}
