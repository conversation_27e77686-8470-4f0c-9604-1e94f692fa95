package cn.taken.ad.logic.adv.ainian;

import cn.taken.ad.component.http.apache.FastHttpClient;
import cn.taken.ad.component.http.apache.HttpResult;
import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.encryption.Aes;
import cn.taken.ad.component.utils.encryption.Base64;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.constant.business.*;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.logic.AdvProcessor;
import cn.taken.ad.logic.adv.ainian.dto.Request;
import cn.taken.ad.logic.adv.ainian.dto.Response;
import cn.taken.ad.logic.adv.ainian.dto.req.*;
import cn.taken.ad.logic.adv.ainian.dto.resp.Image;
import cn.taken.ad.logic.adv.ainian.dto.resp.Video;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbReqAdvBillParam;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.*;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseMiniProgramDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseTrackDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseVideoDto;
import cn.taken.ad.logic.base.rtb.response.dto.TagResponseDto;
import cn.taken.ad.utils.ListUtils;
import cn.taken.ad.utils.ParamParser;
import cn.taken.ad.utils.time.TimeUtils;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component("AINIAN" + LogicSuffix.ADV_LOGIC_SUFFIX)
public class AiNianAdvProcessor implements AdvProcessor {
    private static final String MAIN_ID = "mainId";
    private static final String PRICE_KEY = "priceKey";

    @Override
    public RtbResponseDto reqAdv(FastHttpClient httpClient, RtbRequestDto rtbDto, RtbAdvDto advDto) throws Throwable {
        Request request = new Request();
        Map<String, String> appParams = ParamParser.parseParamByJson(advDto.getAppPnyParam());
        request.setId(rtbDto.getReqId());
        request.setApp(createReqApp(rtbDto, advDto, appParams.get(MAIN_ID)));
        request.setImp(createImp(rtbDto, advDto));
        request.setDevice(createReqDevice(rtbDto));
        request.setUser(createUser(rtbDto));
        request.setDebug(false);
        advDto.setReqObj(request);
        String jsonText = JsonHelper.toJsonStringWithoutNull(request);
        HttpResult result = httpClient.postJson(advDto.getRtburl(), jsonText, "utf-8", new Header[]{
                new BasicHeader("Content-Type", "application/json"),
                new BasicHeader("Accept-Encoding", "gzip")
        }, advDto.getTimeout());
        RtbResponseDto fail = isHttpRequestFail(result);
        if (fail != null) {
            advDto.setRespObj(fail);
            return fail;
        }
        return parseResponse(httpClient, result, rtbDto, advDto);
    }

    private RtbResponseDto parseResponse(FastHttpClient httpClient, HttpResult result, RtbRequestDto rtbDto, RtbAdvDto advDto) {
        Response response = null;
        try {
            response = result.getDataObjectByJson(Response.class);
        } catch (Exception e) {
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), "Serializable resp fail");
        }
        if (response.getCode() != 0) {
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), response.getCode() + "");
        }
        if (CollectionUtils.isEmpty(response.getSeatbids())) {
            return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
        }
        RtbResponseDto responseDto = new RtbResponseDto(LogicState.SUCCESS.getCode(), "", response.getCode() + "");
        response.getSeatbids().forEach(seatbid -> {
            TagResponseDto tag = new TagResponseDto();
            tag.setTagInfoId(seatbid.getImpId());
            if (seatbid.getPrice() != null) {
                tag.setPrice(seatbid.getPrice().doubleValue());
            }
            tag.setTitle(seatbid.getTitle());
            tag.setDesc(seatbid.getDesc());
            tag.setMaterialType(MaterialType.TEXT);
            if (!CollectionUtils.isEmpty(seatbid.getImageList())) {
                tag.setMaterialType(MaterialType.IMAGE_TEXT);
                Image image = seatbid.getImageList().get(0);
                tag.setMaterialWidth(image.getW());
                tag.setMaterialHeight(image.getH());
                tag.setImgUrls(seatbid.getImageList()
                        .stream()
                        .filter(v -> StringUtils.isNotBlank(v.getUrl()))
                        .map(v -> v.getUrl())
                        .collect(Collectors.toList()));
            }
            List<ResponseTrackDto> trackDtos = new LinkedList<>();
            tag.setTracks(trackDtos);
            if (seatbid.getVideo() != null) {
                tag.setMaterialType(MaterialType.VIDEO);
                ResponseVideoDto videoDto = new ResponseVideoDto();
                Video video = seatbid.getVideo();
                tag.setMaterialWidth(video.getW());
                tag.setMaterialHeight(video.getH());
                videoDto.setVideoUrl(video.getUrl());
                videoDto.setDuration(video.getDuration());
                videoDto.setSkipSeconds(video.getForceDuration());
                if (StringUtils.isNotBlank(video.getCoverUrl())) {
                    videoDto.setCoverImgUrls(ListUtils.asList(video.getCoverUrl()));
                }
                if (!CollectionUtils.isEmpty(video.getVideoTracks())) {
                    video.getVideoTracks().forEach(v -> {
                        if (v.getUrlList() == null || v.getUrlList().isEmpty()) {
                            return;
                        }
                        if ("v_load".equals(v.getType())) {
                            trackDtos.add(new ResponseTrackDto(EventType.VIDEO_LOADED_SUCCESS.getType(), v.getUrlList()));
                        } else if ("v_s".equals(v.getType())) {
                            trackDtos.add(new ResponseTrackDto(EventType.VIDEO_BEGIN.getType(), v.getUrlList()));
                        } else if ("v_b".equals(v.getType())) {
                            trackDtos.add(new ResponseTrackDto(EventType.VIDEO_END.getType(), v.getUrlList()));
                        } else if ("v_a".equals(v.getType())) {
                            trackDtos.add(new ResponseTrackDto(EventType.VIDEO_PLAY_REPLAY.getType(), v.getUrlList()));
                        } else if ("v_sp".equals(v.getType())) {
                            trackDtos.add(new ResponseTrackDto(EventType.VIDEO_PAUSE.getType(), v.getUrlList()));
                        } else if ("v_c".equals(v.getType())) {
                            trackDtos.add(new ResponseTrackDto(EventType.VIDEO_CONTINUE.getType(), v.getUrlList()));
                        } else if ("v_fu".equals(v.getType())) {
                            trackDtos.add(new ResponseTrackDto(EventType.VIDEO_FULL_SCREEN.getType(), v.getUrlList()));
                        } else if ("v_ex".equals(v.getType())) {
                            trackDtos.add(new ResponseTrackDto(EventType.VIDEO_EXITS_FULL_SCREEN.getType(), v.getUrlList()));
                        } else if ("v_sk".equals(v.getType())) {
                            trackDtos.add(new ResponseTrackDto(EventType.VIDEO_SKIP.getType(), v.getUrlList()));
                        } else if ("v_mu".equals(v.getType())) {
                            trackDtos.add(new ResponseTrackDto(EventType.VIDEO_MUTE.getType(), v.getUrlList()));
                        } else if ("v_umu".equals(v.getType())) {
                            trackDtos.add(new ResponseTrackDto(EventType.VIDEO_UNMUTE.getType(), v.getUrlList()));
                        }
                    });
                }
                tag.setVideoInfo(videoDto);
            }
            tag.setIconUrl(seatbid.getIcon());
            tag.setClickUrl(seatbid.getLandingUrl());
            tag.setDeepLinkUrl(seatbid.getDeeplinkUrl());
            tag.setActionType(ActionType.WEB_VIEW_H5);
            String clickId = null;
            if (seatbid.getAdOperationType() == 2) {
                tag.setActionType(ActionType.DOWNLOAD);
            } else if (seatbid.getAdOperationType() == 3) {//广点通落地页
                HttpResult gdt = httpClient.get(seatbid.getLandingUrl(), null, null, advDto.getTimeout());
                if (gdt.isSuccess()) {
                    JsonObject obj = JsonHelper.fromJson(JsonElement.class, result.getDataStringUTF8()).getAsJsonObject();
                    if (obj.get("ret").getAsInt() == 0) {
                        JsonObject data = obj.get("data").getAsJsonObject();
                        tag.setClickUrl(data.get("dstlink").getAsString());
                        clickId = data.get("clickid").getAsString();
                    }
                }
            }
            tag.setWinNoticeUrls(new ArrayList<>());
            tag.setFailNoticeUrls(new ArrayList<>());
            if (!CollectionUtils.isEmpty(seatbid.getTrackList())) {
                seatbid.getTrackList().forEach(v -> {
                    if (CollectionUtils.isEmpty(v.getUrlList())) {
                        return;
                    }
                    if ("show".equals(v.getType())) {
                        trackDtos.add(new ResponseTrackDto(EventType.EXPOSURE.getType(), v.getUrlList()));
                    } else if ("click".equals(v.getType())) {
                        trackDtos.add(new ResponseTrackDto(EventType.CLICK.getType(), v.getUrlList()));
                    } else if ("dpb".equals(v.getType())) {
                        trackDtos.add(new ResponseTrackDto(EventType.DEEPLINK_START.getType(), v.getUrlList()));
                    } else if ("dps".equals(v.getType())) {
                        trackDtos.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_SUCCESS.getType(), v.getUrlList()));
                    } else if ("dpf".equals(v.getType())) {
                        trackDtos.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_FAIL.getType(), v.getUrlList()));
                    } else if ("ds".equals(v.getType())) {
                        trackDtos.add(new ResponseTrackDto(EventType.DOWNLOAD_BEGIN.getType(), v.getUrlList()));
                    } else if ("de".equals(v.getType())) {
                        trackDtos.add(new ResponseTrackDto(EventType.DOWNLOAD_COMPLETED.getType(), v.getUrlList()));
                    } else if ("si".equals(v.getType())) {
                        trackDtos.add(new ResponseTrackDto(EventType.INSTALL_BEGIN.getType(), v.getUrlList()));
                    } else if ("se".equals(v.getType())) {
                        trackDtos.add(new ResponseTrackDto(EventType.INSTALL_COMPLETED.getType(), v.getUrlList()));
                    } else if ("aa".equals(v.getType())) {
                        trackDtos.add(new ResponseTrackDto(EventType.ACTIVE_APP.getType(), v.getUrlList()));
                    } else if ("loss".equals(v.getType())) {
                        tag.getFailNoticeUrls().addAll(v.getUrlList());
                    }
                });
            }
            if (seatbid.getApplet() != null) {
                ResponseMiniProgramDto miniProgram = new ResponseMiniProgramDto();
                miniProgram.setId(seatbid.getApplet().getId());
                miniProgram.setPath(seatbid.getApplet().getPath());
                tag.setMiniProgram(miniProgram);
            }
            if (StringUtils.isNotBlank(seatbid.getClickAreaReportUrl())) {
                tag.setClickAreaReportUrls(ListUtils.asList(seatbid.getClickAreaReportUrl()));
            }
            String priceKey = ParamParser.parseParamByJson(advDto.getPnyParam()).get(PRICE_KEY);
            String finalClickId = clickId;
            //宏替换
            tag.getTracks().forEach(track -> {
                List<String> urls = track.getTrackUrls();
                urls = replaceMacro("__ad_width__", urls, MacroType.WIDTH.getCode());
                urls = replaceMacro("__ad_height__", urls, MacroType.HEIGHT.getCode());
                urls = replaceMacro("__down_x__", urls, MacroType.ABS_DOWN_X.getCode());
                urls = replaceMacro("__down_y__", urls, MacroType.ABS_DOWN_Y.getCode());
                urls = replaceMacro("__up_x__", urls, MacroType.ABS_UP_X.getCode());
                urls = replaceMacro("__up_y__", urls, MacroType.ABS_UP_Y.getCode());
                urls = replaceMacro("__r_down_x__", urls, MacroType.DOWN_X.getCode());
                urls = replaceMacro("__r_down_y__", urls, MacroType.DOWN_Y.getCode());
                urls = replaceMacro("__r_up_x__", urls, MacroType.UP_X.getCode());
                urls = replaceMacro("__r_up_y__", urls, MacroType.UP_Y.getCode());
                urls = replaceMacro("__event_time_start__", urls, MacroType.START_TIME.getCode());
                urls = replaceMacro("__event_time_end__", urls, MacroType.END_TIME.getCode());
                if (finalClickId != null) {
                    urls = replaceMacro("__click_id__", urls, finalClickId);
                }
                urls = replaceMacro("__sld__", urls, MacroType.SLD.getCode());
                urls = replaceMacro("__progress__", urls, MacroType.VIDEO_PROGRESS.getCode());
                urls = replaceMacro("__video_duration__", urls, MacroType.VIDEO_PROGRESS_SEC.getCode());
                if (seatbid.getPrice() != null) {
                    String ep = enPrice(seatbid.getPrice().toString(), priceKey);
                    if (ep != null) {
                        urls = replaceMacro("__accomplished__", urls, ep);
                    }
                }
                track.setTrackUrls(urls);
            });
            responseDto.getTags().add(tag);
        });
        return responseDto;
    }

    private User createUser(RtbRequestDto rtbDto) {
        if (rtbDto.getUser() == null) {
            return null;
        }
        User user = new User();
        RequestUserDto userDto = rtbDto.getUser();
        user.setId(userDto.getUserId());
        user.setAge(userDto.getAge());
        if ("M".equals(userDto.getGender())) {
            user.setSex(1);
        } else if ("F".equals(userDto.getGender())) {
            user.setSex(2);
        } else {
            user.setSex(0);
        }
        if (userDto.getInterest() != null) {
            user.setKeywords(StringUtils.join(userDto.getInterest(), ","));
        }
        return user;
    }

    private Device createReqDevice(RtbRequestDto rtbDto) {
        Device device = new Device();
        RequestDeviceDto deviceDto = rtbDto.getDevice();
        device.setDevicet(0);
        if (deviceDto.getDeviceType() != null) {
            if (DeviceType.PAD == deviceDto.getDeviceType()) {
                device.setDevicet(1);
            }
        }
        device.setOst(0);
        if (OsType.ANDROID == deviceDto.getOsType()) {
            device.setOst(1);
        } else if (OsType.IOS == deviceDto.getOsType()) {
            device.setOst(2);
        } else if (OsType.HARMONY == deviceDto.getOsType()) {
            device.setOst(3);
        }
        device.setOsv(deviceDto.getOsVersion());

        DidInfo didInfo = new DidInfo();
        didInfo.setAndroidId(deviceDto.getAndroidId());
        didInfo.setAndroidIdMd5(deviceDto.getAndroidIdMd5());
        didInfo.setOaid(deviceDto.getOaid());
        didInfo.setOaidMd5(deviceDto.getOaidMd5());
        didInfo.setImei(deviceDto.getImei());
        didInfo.setImeiMd5(deviceDto.getImeiMd5());
        didInfo.setIdfa(deviceDto.getIdfa());
        didInfo.setIdfaMd5(deviceDto.getIdfaMd5());
        if (!CollectionUtils.isEmpty(deviceDto.getCaids())) {
            RequestCaidDto caidDto = deviceDto.getCaids().get(0);
            didInfo.setCaid(caidDto.getCaid());
            didInfo.setCaidVer(caidDto.getVersion());
        }
        didInfo.setIdfv(deviceDto.getIdfa());
        didInfo.setUdid(deviceDto.getOpenUdId());
        // gaid/gaidMd5
        device.setDidInfo(didInfo);

        GeoInfo geoInfo = new GeoInfo();
        geoInfo.setUserAgent(deviceDto.getUserAgent());
        geoInfo.setIp(rtbDto.getNetwork().getIp());
        geoInfo.setIpv6(rtbDto.getNetwork().getIpv6());
        if (rtbDto.getGeo() != null) {
            if (rtbDto.getGeo().getLongitude() != null) {
                geoInfo.setLongitude(rtbDto.getGeo().getLongitude().floatValue());
            }
            if (rtbDto.getGeo().getLatitude() != null) {
                geoInfo.setLatitude(rtbDto.getGeo().getLatitude().floatValue());
            }
            if (rtbDto.getGeo().getCoordinateType() != null) {
                if (CoordinateType.GLOBAL == rtbDto.getGeo().getCoordinateType()) {
                    geoInfo.setCoordinateType(1);
                } else if (CoordinateType.STATE == rtbDto.getGeo().getCoordinateType()) {
                    geoInfo.setCoordinateType(2);
                } else if (CoordinateType.BAIDU == rtbDto.getGeo().getCoordinateType()) {
                    geoInfo.setCoordinateType(3);
                }
            }
        }
        if (StringUtils.isNotBlank(rtbDto.getNetwork().getMac())) {
            geoInfo.setMac(rtbDto.getNetwork().getMac().toUpperCase());
        }
        geoInfo.setMacMd5(rtbDto.getNetwork().getMacMd5());
        geoInfo.setImsi(deviceDto.getImsi());
        geoInfo.setSsid(rtbDto.getNetwork().getSsid());
        device.setGeoInfo(geoInfo);

        device.setModel(deviceDto.getModel());
        device.setBrand(deviceDto.getBrand());

        device.setMake(deviceDto.getVendor());
        device.setSw(deviceDto.getWidth());
        device.setSh(deviceDto.getHeight());
        if (OrientationType.HORIZONTAL == deviceDto.getOrientation()) {
            device.setSt(2);
        } else if (OrientationType.VERTICAL == deviceDto.getOrientation()) {
            device.setSt(1);
        }
        device.setCarrier(0);
        CarrierType carrier = rtbDto.getNetwork().getCarrierType();
        if (CarrierType.CM == carrier) {
            device.setCarrier(1);
        } else if (CarrierType.CU == carrier) {
            device.setCarrier(2);
        } else if (CarrierType.CT == carrier) {
            device.setCarrier(3);
        }
        device.setConnectionType(0);
        ConnectionType connectionType = rtbDto.getNetwork().getConnectType();
        if (ConnectionType.WIFI == connectionType) {
            device.setConnectionType(4);
        } else if (ConnectionType.ETHERNET == connectionType) {
            device.setConnectionType(5);
        } else if (ConnectionType.NETWORK_3G == connectionType) {
            device.setConnectionType(1);
        } else if (ConnectionType.NETWORK_4G == connectionType) {
            device.setConnectionType(2);
        } else if (ConnectionType.NETWORK_5G == connectionType) {
            device.setConnectionType(3);
        }
        if (deviceDto.getScreenInch() != null) {
            device.setInch(deviceDto.getScreenInch().floatValue());
        }
        if (deviceDto.getScreenDensity() != null) {
            device.setDensity(deviceDto.getScreenDensity().floatValue());
            device.setDpi((int) (deviceDto.getScreenDensity() * 160));
        }
        device.setPpi(deviceDto.getPpi());
        if (!CollectionUtils.isEmpty(deviceDto.getInstalledAppInfo())) {
            List<String> apks = deviceDto.getInstalledAppInfo()
                    .stream()
                    .filter(v -> StringUtils.isNotBlank(v.getPackageName()))
                    .map(v -> v.getPackageName()).collect(Collectors.toList());
            device.setAppList(apks);
        }
        device.setRomVersion(deviceDto.getRomVersion());
        device.setAppStoreVer(deviceDto.getAppStoreVersion());
        if (StringUtils.isNotBlank(deviceDto.getSysCompileTime())) {
            Long time = TimeUtils.convertMilliSecond(deviceDto.getSysCompileTime());
            if (time != null) {
                device.setSyscmpTime((time / 1000) + "");
            }
        }
        device.setStartupTime(deviceDto.getSysStartTime());
        device.setSysInitTime(deviceDto.getSysInitTime());
        device.setHmv(deviceDto.getHmsVersion());
        device.setHwv(deviceDto.getHmsAgVersion());
        device.setBootMark(deviceDto.getBootMark());
        device.setUpdateMark(deviceDto.getUpdateMark());
        device.setDeviceName(deviceDto.getDeviceName());
        device.setDeviceNameMd5(deviceDto.getDeviceNameMd5());
        if (deviceDto.getBatteryPower() != null) {
            device.setBattery(deviceDto.getBatteryPower() + "");
        }
        device.setHardwareModel(deviceDto.getHardwareModel());
        if (deviceDto.getDeviceMemory() != null) {
            device.setMemorySize((deviceDto.getDeviceMemory() / 1024) + "");
        }
        if (deviceDto.getDeviceHardDisk() != null) {
            device.setHardDiskSize((deviceDto.getDeviceHardDisk() / 1024) + "");
        }
        if (deviceDto.getApiLevel() != null) {
            device.setApiLevel(deviceDto.getApiLevel() + "");
        }
        device.setPaid(deviceDto.getPaid());
        device.setAaid(deviceDto.getAaid());
        return device;
    }

    private Imp createImp(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        RequestTagDto tagDto = rtbDto.getTag();
        Imp imp = new Imp();
        imp.setId("1");
        imp.setTagId(advDto.getTagCode());
        imp.setW(tagDto.getWidth());
        imp.setH(tagDto.getHeight());
        imp.setPos(4);
        if (tagDto.getTagType() != null) {
            switch (tagDto.getTagType()) {
                case OPEN:
                    imp.setPos(1);
                    break;
                case INTERSTITIAL:
                    imp.setPos(3);
                    break;
                case INFORMATION_FLOW:
                    imp.setPos(4);
                    break;
                case NORMAL_VIDEO:
                    imp.setPos(5);
                    break;
                case INCENTIVE_VIDEO:
                    imp.setPos(6);
                    break;
                case FULL_SCREEN_VIDEO:
                    imp.setPos(2);
                    break;
            }
        }
        if (tagDto.getPrice() != null) {
            imp.setBid(true);
            imp.setBidFloor(tagDto.getPrice().intValue());
        }
        return imp;
    }

    private App createReqApp(RtbRequestDto rtbDto, RtbAdvDto advDto, String mainId) {
        RequestAppDto appDto = rtbDto.getApp();
        App app = new App();
        app.setMainId(Long.valueOf(mainId));
        app.setAppId(advDto.getAppCode());
        app.setName(appDto.getAppName());
        app.setPkg(appDto.getBundle());
        app.setVer(appDto.getAppVersion());
        app.setDeepLink(true);
        app.setSecure(rtbDto.getTag().getNeedHttps() != null && rtbDto.getTag().getNeedHttps() ? 1 : 0);
        app.setGdt(true);
        return app;
    }

    @Override
    public SuperResult<String> billResult(FastHttpClient httpClient, RtbReqAdvBillParam param) throws Exception {
        List<String> urls = param.getUrls();
        if (CollectionUtils.isEmpty(urls)) {
            return SuperResult.rightResult();
        }
        boolean success = false;
        String msg = "not success";
        for (String url : urls) {
            if (param.getPrice() != null) {
                url = url.replace("__WIN_ECPM__", param.getPrice().toString());
            }
            HttpResult result = httpClient.get(url, null, null, -1);
            if (result.isSuccess()) {
                success = true;
            } else {
                msg = result.getStatusLine().toString();
            }
        }
        return success ? SuperResult.rightResult() : SuperResult.badResult(msg);
    }

    private String enPrice(String price, String key) {
        try {
            byte[] values = Aes.encrypt(price, key);
            return Base64.safeUrlBase64Encode(values);
        } catch (Exception e) {
            return null;
        }
    }
}
