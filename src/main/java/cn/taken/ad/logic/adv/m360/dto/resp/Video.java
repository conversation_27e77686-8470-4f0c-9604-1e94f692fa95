package cn.taken.ad.logic.adv.m360.dto.resp;

import java.util.List;

public class Video {
    private String url; //Y 视频地址URL
    private Integer duration; // 视频时长秒数
    private String video_md5; // 视频文件的MD5值
    private Integer video_size; // 视频文件的大小，单位是字节
    private List<String> start_tracks; //Y 视频开始播放监测URL列表
    private List<String> pause_tracks; //Y 视频暂停播放监测URL列表
    private List<String> conti_tracks; //Y 视频继续播放监测URL列表
    private List<String> exit_tracks; //Y 视频关闭播放监测URL列表
    private List<String> comp_tracks; //Y 视频完成播放监测URL列表
    private List<TShowTrack> TShowTrack; //Y 视频已播放时长秒数监测，注意可能会有多个

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getVideo_md5() {
        return video_md5;
    }

    public void setVideo_md5(String video_md5) {
        this.video_md5 = video_md5;
    }

    public Integer getVideo_size() {
        return video_size;
    }

    public void setVideo_size(Integer video_size) {
        this.video_size = video_size;
    }

    public List<String> getStart_tracks() {
        return start_tracks;
    }

    public void setStart_tracks(List<String> start_tracks) {
        this.start_tracks = start_tracks;
    }

    public List<String> getPause_tracks() {
        return pause_tracks;
    }

    public void setPause_tracks(List<String> pause_tracks) {
        this.pause_tracks = pause_tracks;
    }

    public List<String> getConti_tracks() {
        return conti_tracks;
    }

    public void setConti_tracks(List<String> conti_tracks) {
        this.conti_tracks = conti_tracks;
    }

    public List<String> getExit_tracks() {
        return exit_tracks;
    }

    public void setExit_tracks(List<String> exit_tracks) {
        this.exit_tracks = exit_tracks;
    }

    public List<String> getComp_tracks() {
        return comp_tracks;
    }

    public void setComp_tracks(List<String> comp_tracks) {
        this.comp_tracks = comp_tracks;
    }

    public List<TShowTrack> getTShowTrack() {
        return TShowTrack;
    }

    public void setTShowTrack(List<TShowTrack> TShowTrack) {
        this.TShowTrack = TShowTrack;
    }
}
