package cn.taken.ad.logic.adv.busi.dto;

import java.io.Serializable;
import java.util.List;

/**
 * MADBOXI监测链接信息
 */
public class MadBoxiResponseTrack implements Serializable {
    private static final long serialVersionUID = -831049895855023139L;
    
    /**
     * 监测类型
     */
    private Integer type;
    
    /**
     * 监测链接列表
     */
    private List<String> urls;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public List<String> getUrls() {
        return urls;
    }

    public void setUrls(List<String> urls) {
        this.urls = urls;
    }
}
