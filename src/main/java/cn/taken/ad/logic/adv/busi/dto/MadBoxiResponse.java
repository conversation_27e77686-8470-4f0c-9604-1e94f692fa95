package cn.taken.ad.logic.adv.busi.dto;

import java.io.Serializable;
import java.util.List;

/**
 * MADBOXI响应主体
 */
public class MadBoxiResponse implements Serializable {
    private static final long serialVersionUID = -831049895855023133L;
    
    /**
     * 请求 ID
     */
    private String id;
    
    /**
     * 响应码，0 成功 非 0 无填充或失败
     */
    private Integer code;

    /**
     * 广告列表
     */
    private List<MadBoxiResponseAd> ads;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public List<MadBoxiResponseAd> getAds() {
        return ads;
    }

    public void setAds(List<MadBoxiResponseAd> ads) {
        this.ads = ads;
    }
}
