package cn.taken.ad.logic.adv.ainian.dto.req;

import java.util.List;

public class Device {
    private Integer devicet; //设备类型：0 = 手机，1平板
    private Integer ost; //系统类型：0=未知，1=安卓，2=ios,3=鸿蒙
    private String osv; //系统版本号
    private DidInfo didInfo; //设备唯一标识：详见DidInfo
    private GeoInfo geoInfo; //设备当前地理位置：详见GeoInfo
    private String model; //设备型号
    private String brand; //品牌
    private String make; //生产厂商
    private Integer sw; //屏幕的宽(像素).
    private Integer sh; //屏幕的高(像素).
    private Integer st; //屏幕方向：1竖屏，2横屏
    private Integer carrier; //0=未知；1=移动；2=联通；3=电信
    private Integer connectionType; //0=未知，1=3G，2=4G，3=5G，4=WIFI，5=ethernet
    private Float inch; //屏幕尺寸
    private Integer dpi; //屏幕像素密度
    private Integer ppi; //屏幕每英寸像素数目
    private List<String> appList; //设备安装的APP包名列表
    private String romVersion; //手机 ROM 的版本号
    private String appStoreVer; //应⽤商店版本号
    private String syscmpTime; //系统编译时间（时间戳秒）
    private String startupTime; //系统最近一次启动时间
    private String sysInitTime; //设备初始化时间,
    private String hmv; //华为应用市场的版本号
    private String hwv; //华为AG的版本
    private String bootMark; //统启动标识
    private String updateMark; //系统更新标识
    private String deviceName; //设备名称
    private String deviceNameMd5; //设备名称deviceName MD5
    private Float density; //设备屏幕密度
    private String battery; //电池电量百分比
    private String hardwareModel; //设备model值
    private String memorySize; //物理内存KB
    private String hardDiskSize; //硬盘大小KB
    private String apiLevel; //安卓API-Level
    private String paid; //拼多多广告标识
    private String aaid; //阿里广告标识


    public Integer getDevicet() {
        return devicet;
    }

    public void setDevicet(Integer devicet) {
        this.devicet = devicet;
    }

    public Integer getOst() {
        return ost;
    }

    public void setOst(Integer ost) {
        this.ost = ost;
    }

    public String getOsv() {
        return osv;
    }

    public void setOsv(String osv) {
        this.osv = osv;
    }

    public DidInfo getDidInfo() {
        return didInfo;
    }

    public void setDidInfo(DidInfo didInfo) {
        this.didInfo = didInfo;
    }

    public GeoInfo getGeoInfo() {
        return geoInfo;
    }

    public void setGeoInfo(GeoInfo geoInfo) {
        this.geoInfo = geoInfo;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getMake() {
        return make;
    }

    public void setMake(String make) {
        this.make = make;
    }

    public Integer getSw() {
        return sw;
    }

    public void setSw(Integer sw) {
        this.sw = sw;
    }

    public Integer getSh() {
        return sh;
    }

    public void setSh(Integer sh) {
        this.sh = sh;
    }

    public Integer getSt() {
        return st;
    }

    public void setSt(Integer st) {
        this.st = st;
    }

    public Integer getCarrier() {
        return carrier;
    }

    public void setCarrier(Integer carrier) {
        this.carrier = carrier;
    }

    public Integer getConnectionType() {
        return connectionType;
    }

    public void setConnectionType(Integer connectionType) {
        this.connectionType = connectionType;
    }

    public Float getInch() {
        return inch;
    }

    public void setInch(Float inch) {
        this.inch = inch;
    }

    public Integer getDpi() {
        return dpi;
    }

    public void setDpi(Integer dpi) {
        this.dpi = dpi;
    }

    public Integer getPpi() {
        return ppi;
    }

    public void setPpi(Integer ppi) {
        this.ppi = ppi;
    }

    public List<String> getAppList() {
        return appList;
    }

    public void setAppList(List<String> appList) {
        this.appList = appList;
    }

    public String getRomVersion() {
        return romVersion;
    }

    public void setRomVersion(String romVersion) {
        this.romVersion = romVersion;
    }

    public String getAppStoreVer() {
        return appStoreVer;
    }

    public void setAppStoreVer(String appStoreVer) {
        this.appStoreVer = appStoreVer;
    }

    public String getSyscmpTime() {
        return syscmpTime;
    }

    public void setSyscmpTime(String syscmpTime) {
        this.syscmpTime = syscmpTime;
    }

    public String getStartupTime() {
        return startupTime;
    }

    public void setStartupTime(String startupTime) {
        this.startupTime = startupTime;
    }

    public String getSysInitTime() {
        return sysInitTime;
    }

    public void setSysInitTime(String sysInitTime) {
        this.sysInitTime = sysInitTime;
    }

    public String getHmv() {
        return hmv;
    }

    public void setHmv(String hmv) {
        this.hmv = hmv;
    }

    public String getHwv() {
        return hwv;
    }

    public void setHwv(String hwv) {
        this.hwv = hwv;
    }

    public String getBootMark() {
        return bootMark;
    }

    public void setBootMark(String bootMark) {
        this.bootMark = bootMark;
    }

    public String getUpdateMark() {
        return updateMark;
    }

    public void setUpdateMark(String updateMark) {
        this.updateMark = updateMark;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceNameMd5() {
        return deviceNameMd5;
    }

    public void setDeviceNameMd5(String deviceNameMd5) {
        this.deviceNameMd5 = deviceNameMd5;
    }

    public Float getDensity() {
        return density;
    }

    public void setDensity(Float density) {
        this.density = density;
    }

    public String getBattery() {
        return battery;
    }

    public void setBattery(String battery) {
        this.battery = battery;
    }

    public String getHardwareModel() {
        return hardwareModel;
    }

    public void setHardwareModel(String hardwareModel) {
        this.hardwareModel = hardwareModel;
    }

    public String getMemorySize() {
        return memorySize;
    }

    public void setMemorySize(String memorySize) {
        this.memorySize = memorySize;
    }

    public String getHardDiskSize() {
        return hardDiskSize;
    }

    public void setHardDiskSize(String hardDiskSize) {
        this.hardDiskSize = hardDiskSize;
    }

    public String getApiLevel() {
        return apiLevel;
    }

    public void setApiLevel(String apiLevel) {
        this.apiLevel = apiLevel;
    }

    public String getPaid() {
        return paid;
    }

    public void setPaid(String paid) {
        this.paid = paid;
    }

    public String getAaid() {
        return aaid;
    }

    public void setAaid(String aaid) {
        this.aaid = aaid;
    }
}
