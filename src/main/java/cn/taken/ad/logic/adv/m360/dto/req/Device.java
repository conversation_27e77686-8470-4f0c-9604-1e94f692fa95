package cn.taken.ad.logic.adv.m360.dto.req;

import java.util.List;

public class Device {

    private List<DeviceId> device_id; //Y DeviceId对象列表，建议尽可能多填写，有助于提高广告变现能力
    private Integer os_type; //Y 操作系统类型 <br>{<br>OS_IOS = 1;<br>OS_ANDROID = 2;<br>}
    private String os_version; //Y 操作系统版本号，需从系统API获取的原始数据
    private String brand; //Y 设备品牌，Android取Build.BRAND，可用Build.MANUFACTURE代替，但效果会变差；iOS使用默认值Apple
    private String model; //Y 设备型号，Android取Build.MODEL
    private Integer device_type; // 设备类型<br>{<br>UNKNOWN = 0; //未知<br>TABLET = 1; //平板<br>PHONE = 2; //手机<br>}<br>
    private String boot_mark; // 开机标志,获取方案见：https://easydoc.soft.360.cn/doc?project=9aa82f66a2bcc7a3720b2c56fcd8da24&doc=ca2a91aa256809f0e652e799fcc49e0c&config=title_menu_toc
    private String update_mark; // 更新标志，获取方法见：https://easydoc.soft.360.cn/doc?project=9aa82f66a2bcc7a3720b2c56fcd8da24&doc=ca2a91aa256809f0e652e799fcc49e0c&config=title_menu_toc
    private Integer screen_width; // 设备屏幕的宽度，以像素为单位，与屏幕密度无关
    private Integer screen_height; // 设备屏幕的高度，以像素为单位，与屏幕密度无关
    private Double screen_density; // 屏幕密度，如果传不了真实值，请统一填写1.0
    private Integer screen_orientation; // 屏幕朝向<br>{<br>SCREEN_ORIENTATION_UNKNOWN = 0; //未知<br>SCREEN_ORIENTATION_PORTRAIT = 1; //竖屏<br>SCREEN_ORIENTATION_LANDSCAPE = 2; //横屏<br>}<br>
    private Integer carrier_id; //Y 运营商编码<br>{<br>UNKNOWN = 0; //未知<br>CHINA_MOBILE = 70120; //中国移动<br>CHINA_TELECOM = 70121; //中国电信<br>UNICOM = 70123; //中国联通<br>}<br>注意不是客户端直接获取的460开头编码，需要转换<br>对应关系查询：Wiki，360百科<br>除移动电信联通外，其他情况均填写未知
    private Integer battery_level; // 当前剩余电量的百分比，取值范围为0至100


    public List<DeviceId> getDevice_id() {
        return device_id;
    }

    public void setDevice_id(List<DeviceId> device_id) {
        this.device_id = device_id;
    }

    public Integer getOs_type() {
        return os_type;
    }

    public void setOs_type(Integer os_type) {
        this.os_type = os_type;
    }

    public String getOs_version() {
        return os_version;
    }

    public void setOs_version(String os_version) {
        this.os_version = os_version;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Integer getDevice_type() {
        return device_type;
    }

    public void setDevice_type(Integer device_type) {
        this.device_type = device_type;
    }

    public String getBoot_mark() {
        return boot_mark;
    }

    public void setBoot_mark(String boot_mark) {
        this.boot_mark = boot_mark;
    }

    public String getUpdate_mark() {
        return update_mark;
    }

    public void setUpdate_mark(String update_mark) {
        this.update_mark = update_mark;
    }

    public Integer getScreen_width() {
        return screen_width;
    }

    public void setScreen_width(Integer screen_width) {
        this.screen_width = screen_width;
    }

    public Integer getScreen_height() {
        return screen_height;
    }

    public void setScreen_height(Integer screen_height) {
        this.screen_height = screen_height;
    }

    public Double getScreen_density() {
        return screen_density;
    }

    public void setScreen_density(Double screen_density) {
        this.screen_density = screen_density;
    }

    public Integer getScreen_orientation() {
        return screen_orientation;
    }

    public void setScreen_orientation(Integer screen_orientation) {
        this.screen_orientation = screen_orientation;
    }

    public Integer getCarrier_id() {
        return carrier_id;
    }

    public void setCarrier_id(Integer carrier_id) {
        this.carrier_id = carrier_id;
    }

    public Integer getBattery_level() {
        return battery_level;
    }

    public void setBattery_level(Integer battery_level) {
        this.battery_level = battery_level;
    }
}
