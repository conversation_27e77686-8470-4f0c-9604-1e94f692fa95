package cn.taken.ad.logic.adv.ainian.dto.req;

public class DidInfo {
    private String androidId; //Android 手机 androidId 明文 值
    private String androidIdMd5; //Android 手机 androidId Md5 值
    private String oaid; //Android 手机 oaid 值
    private String oaidMd5; //Android 手机 oaid Md5 值
    private String imei; //Android 手机 imei 值
    private String imeiMd5; //Android 手机 imei Md5 值
    private String idfa; //ios设备明文
    private String idfaMd5; //ios设备明文 Md5值
    private String caid; //ios caid
    private String caidVer; //ios caid 版本号
    private String idfv; //ios设备明文
    private String udid; //ios 设备唯一识别号
    private String gaid; //设备唯一id
    private String gaidMd5; //设备唯一id MD5

    public String getAndroidId() {
        return androidId;
    }

    public void setAndroidId(String androidId) {
        this.androidId = androidId;
    }

    public String getAndroidIdMd5() {
        return androidIdMd5;
    }

    public void setAndroidIdMd5(String androidIdMd5) {
        this.androidIdMd5 = androidIdMd5;
    }

    public String getOaid() {
        return oaid;
    }

    public void setOaid(String oaid) {
        this.oaid = oaid;
    }

    public String getOaidMd5() {
        return oaidMd5;
    }

    public void setOaidMd5(String oaidMd5) {
        this.oaidMd5 = oaidMd5;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getImeiMd5() {
        return imeiMd5;
    }

    public void setImeiMd5(String imeiMd5) {
        this.imeiMd5 = imeiMd5;
    }

    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        this.idfa = idfa;
    }

    public String getIdfaMd5() {
        return idfaMd5;
    }

    public void setIdfaMd5(String idfaMd5) {
        this.idfaMd5 = idfaMd5;
    }

    public String getCaid() {
        return caid;
    }

    public void setCaid(String caid) {
        this.caid = caid;
    }

    public String getCaidVer() {
        return caidVer;
    }

    public void setCaidVer(String caidVer) {
        this.caidVer = caidVer;
    }

    public String getIdfv() {
        return idfv;
    }

    public void setIdfv(String idfv) {
        this.idfv = idfv;
    }

    public String getUdid() {
        return udid;
    }

    public void setUdid(String udid) {
        this.udid = udid;
    }

    public String getGaid() {
        return gaid;
    }

    public void setGaid(String gaid) {
        this.gaid = gaid;
    }

    public String getGaidMd5() {
        return gaidMd5;
    }

    public void setGaidMd5(String gaidMd5) {
        this.gaidMd5 = gaidMd5;
    }
}
