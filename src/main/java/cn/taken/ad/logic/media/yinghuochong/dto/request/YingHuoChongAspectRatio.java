package cn.taken.ad.logic.media.yinghuochong.dto.request;

public enum YingHuoChongAspectRatio {
    RATIO_38X25(1),
    RATIO_9X16(2),
    RATIO_16X9(3),
    RATIO_2X3(4),
    RATIO_3X2(5),
    RATIO_2X1(6),
    RATIO_1X2(7);

    private final int code;

    YingHuoChongAspectRatio(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static YingHuoChongAspectRatio findByCode(int code) {
        for (YingHuoChongAspectRatio ratio : YingHuoChongAspectRatio.values()) {
            if (ratio.code == code) {
                return ratio;
            }
        }
        return RATIO_38X25; // 默认返回38:25
    }
}
