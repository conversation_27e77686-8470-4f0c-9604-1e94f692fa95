package cn.taken.ad.logic.adv.busi.dto;

import java.io.Serializable;

/**
 * MADBOXI网络信息
 */
public class MadBoxiRequestNetwork implements Serializable {
    private static final long serialVersionUID = -7064608428216363194L;
    
    /**
     * 连接类型 0:未知 1:WiFi 2:2G 3:3G 4:4G 5:5G 10:以太网
     */
    private Integer connectionType;
    
    /**
     * 运营商 0:未知 1:移动 2:联通 3:电信
     */
    private Integer carrier;
    
    /**
     * LAC
     */
    private String lac;
    
    /**
     * CID
     */
    private String cid;
    
    /**
     * SSID
     */
    private String ssid;
    
    /**
     * BSSID
     */
    private String bssid;

    public Integer getConnectionType() {
        return connectionType;
    }

    public void setConnectionType(Integer connectionType) {
        this.connectionType = connectionType;
    }

    public Integer getCarrier() {
        return carrier;
    }

    public void setCarrier(Integer carrier) {
        this.carrier = carrier;
    }

    public String getLac() {
        return lac;
    }

    public void setLac(String lac) {
        this.lac = lac;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getSsid() {
        return ssid;
    }

    public void setSsid(String ssid) {
        this.ssid = ssid;
    }

    public String getBssid() {
        return bssid;
    }

    public void setBssid(String bssid) {
        this.bssid = bssid;
    }
}
