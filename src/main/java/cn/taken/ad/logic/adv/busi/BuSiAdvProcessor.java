package cn.taken.ad.logic.adv.busi;

import cn.taken.ad.component.http.apache.FastHttpClient;
import cn.taken.ad.component.http.apache.HttpResult;
import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.encryption.Md5;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.constant.business.ActionType;
import cn.taken.ad.constant.business.EventType;
import cn.taken.ad.constant.business.MacroType;
import cn.taken.ad.constant.business.MaterialType;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.logic.AdvProcessor;
import cn.taken.ad.logic.adv.busi.dto.*;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbReqAdvBillParam;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestAppDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestDeviceDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestGeoDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestNetworkDto;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.*;
import cn.taken.ad.utils.ParamParser;
import cn.taken.ad.utils.time.TimeUtils;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * MADBOXI V3.4
 */
@Component("BUSI" + LogicSuffix.ADV_LOGIC_SUFFIX)
public class BuSiAdvProcessor implements AdvProcessor {

    public static final String API_VERSION = "apiVersion";

    private static final Logger log = LoggerFactory.getLogger(BuSiAdvProcessor.class);

    @Override
    public RtbResponseDto reqAdv(FastHttpClient httpClient, RtbRequestDto rtb, RtbAdvDto advDto) throws Throwable {
        MadBoxiAdvRequest request = new MadBoxiAdvRequest();
        String apiVersion = ParamParser.parseParamByJson(advDto.getPnyParam()).get(API_VERSION);
        request.setVersion(apiVersion);
        request.setId(rtb.getReqId());
        request.setSlot_id(advDto.getTagCode());
        request.setImp(convertSlot(rtb, rtb.getTag().getPrice()));
        request.setApp(ConvertApp(rtb.getApp(), advDto));
        request.setDevice(convertDevice(rtb.getDevice(), rtb.getNetwork(), rtb.getGeo()));
        request.setTmax(advDto.getTimeout());
        request.setIs_video("1");

        String json = JsonHelper.toJsonStringWithoutNull(request);
        advDto.setReqObj(json);
        HttpResult httpResult = httpClient.postJson(advDto.getRtburl(), json, "UTF-8", new Header[]{new BasicHeader("Content-Type", "application/json;charset=UTF-8")}, advDto.getTimeout());
        RtbResponseDto fail = isHttpRequestFail(httpResult);
        if (fail != null) {
            advDto.setRespObj(fail);
            return fail;
        }
        String resp = httpResult.getDataStringUTF8();
        advDto.setRespObj(resp);
        if (StringUtils.isEmpty(resp)) {
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), "Resp empty");
        }
        log.info("Request:{}, Resp:{} ", json, resp);
        return parseResponse(rtb, request, resp);
    }

    private MadBoxiRequestApp ConvertApp(RequestAppDto app, RtbAdvDto advDto) {
        MadBoxiRequestApp request = new MadBoxiRequestApp();
        request.setName(StringUtils.isNotEmpty(app.getAppName()) ? app.getAppName() : "");
        request.setVersion(StringUtils.isNotEmpty(app.getAppVersion()) ? app.getAppVersion() : "");
        request.setBundle(StringUtils.isNotEmpty(app.getBundle()) ? app.getBundle() : "");
        return request;
    }

    @Override
    public SuperResult<String> billResult(FastHttpClient httpClient, RtbReqAdvBillParam reqDto) {
        // 是否有请求成功的
        boolean hasRight = false;
        List<String> urls = reqDto.getUrls();
        Double price = reqDto.getPrice();

        if (!reqDto.getBiddingSuccess()) {
            // 竟败
            if (null != price) {
                urls = replaceMacro("__LOSS_PR__", urls, price.toString());
            }
        } else {
            // 竟胜替换价格宏
            if (null != price) {
                urls = replaceMacro("__PRICE__", urls, price.toString());
            }
        }
        for (String url : urls) {
            HttpResult result = httpClient.get(url, null, null, 10000);
            if (!hasRight) {
                hasRight = result.isSuccess();
            }
            //log.info("Req Adv Bill ReqId:{}, Result:{},HttpCode:{},Header:{},ErrMsg:{},url:{}", reqDto.getRtbId(), result.isSuccess(), result.getStatusLine(), Arrays.toString(result.getHeaders()), null != result.getThrowable() ? result.getThrowable().getMessage() : "", url);
        }
        return hasRight ? SuperResult.rightResult() : SuperResult.badResult("no right");
    }

    public RtbResponseDto parseResponse(RtbRequestDto rtbDto, MadBoxiAdvRequest request, String resp) throws Exception {
        MadBoxiResponse response;
        try {
            response = JsonHelper.fromJson(MadBoxiResponse.class, resp);
        } catch (Exception e) {
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), "Resp convert fail");
        }
        if (null == response.getCode()) {
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), "no resp code");
        }
        if (0 != response.getCode()) {
            return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
        }
        if (null == response.getAds() || response.getAds().isEmpty()) {
            return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
        }

        RtbResponseDto responseDto = new RtbResponseDto(LogicState.SUCCESS.getCode(), "", response.getCode() + "");
        response.getAds().forEach(respTagMap -> {
            MadBoxiResponseAd respTag = JsonHelper.fromJson(MadBoxiResponseAd.class, JsonHelper.toJsonString(respTagMap));
            TagResponseDto tagResponseDto = new TagResponseDto();
            // adId 广告ID
            if (respTag.getId() != null) {
                tagResponseDto.setTagInfoId(respTag.getId());
            }
            if (null != respTag.getPrice()) {
                tagResponseDto.setPrice(respTag.getPrice().doubleValue());
            }
            if (respTagMap.get("native") != null) {
                // 获取原生广告信息
                MadBoxiResponseNative nativeAd = JsonHelper.fromJson(MadBoxiResponseNative.class, JsonHelper.toJsonString(respTagMap.get("native")));
                respTag.setNative(nativeAd);
                if (null != nativeAd) {
                    tagResponseDto.setClickUrl(nativeAd.getUrl());
                    tagResponseDto.setDeepLinkUrl(nativeAd.getDeeplink_url());
                    tagResponseDto.setUniversalLink(nativeAd.getUniversal_link());
                    tagResponseDto.setMarketUrl(nativeAd.getMarket_url());
                    ResponseAppDto responseAppDto = new ResponseAppDto();
                    responseAppDto.setAppName(nativeAd.getApp_name());
                    responseAppDto.setAppSize(nativeAd.getApp_size());
                    responseAppDto.setAppVersion(nativeAd.getApp_version());
                    responseAppDto.setAppIconUrl(nativeAd.getApp_icon());
                    responseAppDto.setPackageName(nativeAd.getApp_package());
                    responseAppDto.setAppDeveloper(nativeAd.getDeveloper_name());
                    responseAppDto.setAppPrivacyUrl(nativeAd.getPrivacy_url());
                    responseAppDto.setAppPermissionInfoUrl(nativeAd.getPermission_url());
                    responseAppDto.setAppInfo(nativeAd.getIntroduction_url());
                    tagResponseDto.setAppInfo(responseAppDto);
                    tagResponseDto.setClickAreaReportUrls(Collections.singletonList(nativeAd.getClick_area_report_url()));

                    // 设置监测链接
                    List<ResponseTrackDto> tracks = new ArrayList<>();
                    tagResponseDto.setTracks(tracks);

                    // 解析assets获取标题、描述、图片等信息
                    if (null != nativeAd.getAssets() && !nativeAd.getAssets().isEmpty()) {
                        for (MadBoxiResponseAsset asset : nativeAd.getAssets()) {
                            tagResponseDto.setMaterialType(MaterialType.TEXT);
                            if (asset.getTitle() != null && StringUtils.isNotEmpty(asset.getTitle().getText())) {
                                tagResponseDto.setTitle(asset.getTitle().getText());
                            }

                            if (asset.getData() != null && StringUtils.isNotEmpty(asset.getData().getValue())) {
                                tagResponseDto.setDesc(asset.getData().getValue());
                            }

                            if (asset.getImg() != null && StringUtils.isNotEmpty(asset.getImg().getUrl())) {
                                tagResponseDto.setImgUrls(Collections.singletonList(asset.getImg().getUrl()));
                                tagResponseDto.setMaterialWidth(asset.getImg().getW());
                                tagResponseDto.setMaterialHeight(asset.getImg().getH());
                                tagResponseDto.setMaterialType(MaterialType.IMAGE_TEXT);
                            }

                            if (asset.getVideo() != null && StringUtils.isNotEmpty(asset.getVideo().getVideo_url())) {
                                tagResponseDto.setMaterialType(MaterialType.VIDEO);
                                ResponseVideoDto videoDto = new ResponseVideoDto();
                                videoDto.setVideoUrl(asset.getVideo().getVideo_url());
                                videoDto.setCoverImgUrls(Collections.singletonList(asset.getVideo().getVideo_url()));
                                videoDto.setDuration(asset.getVideo().getDuration());
                                videoDto.setCoverWidth(asset.getVideo().getCover_width());
                                videoDto.setCoverHeight(asset.getVideo().getCover_height());
                                if (asset.getVideo().getEvent_imp() != null) {
                                    asset.getVideo().getEvent_imp().forEach(event -> {
                                        //event 0 播放开始
                                        switch (event.getEvent()) {
                                            case 0:
                                                tracks.add(new ResponseTrackDto(EventType.VIDEO_BEGIN.getType(), event.getImp()));
                                                break;
                                            case 1:
                                                tracks.add(new ResponseTrackDto(EventType.VIDEO_25.getType(), event.getImp()));
                                                break;
                                            case 2:
                                                tracks.add(new ResponseTrackDto(EventType.VIDEO_50.getType(), event.getImp()));
                                                break;
                                            case 3:
                                                tracks.add(new ResponseTrackDto(EventType.VIDEO_75.getType(), event.getImp()));
                                                break;
                                            case 4:
                                                tracks.add(new ResponseTrackDto(EventType.VIDEO_END.getType(), event.getImp()));
                                                break;
                                            case 5:
                                                tracks.add(new ResponseTrackDto(EventType.VIDEO_PAUSE.getType(), event.getImp()));
                                                break;
                                            case 6:
                                                tracks.add(new ResponseTrackDto(EventType.VIDEO_CLOSE.getType(), event.getImp()));
                                                break;
                                        }
                                    });

                                }
                                tagResponseDto.setVideoInfo(videoDto);
                            }
                        }
                    }
                    // 根据interact_type设置交互类型  1:跳转 2:下载类 3:小程序
                    if (null != respTag.getInteract_type()) {
                        switch (respTag.getInteract_type()) {
                            case 1: // 落地页
                                tagResponseDto.setActionType(ActionType.WEB_VIEW_H5);
                                break;
                            case 2: // 下载
                                tagResponseDto.setActionType(ActionType.DOWNLOAD);
                                if (StringUtils.isNotEmpty(nativeAd.getDownload_url())) {
                                    tagResponseDto.setClickUrl(nativeAd.getDownload_url());
                                }
                                break;
                            case 3: // 微信小程序
                                tagResponseDto.setActionType(ActionType.MINI_PROGRAM);
                                ResponseMiniProgramDto responseMiniProgramDto = new ResponseMiniProgramDto();
                                if (StringUtils.isNotEmpty(nativeAd.getWx_mini_user()) && StringUtils.isNotEmpty(nativeAd.getWx_mini_path())) {
                                    responseMiniProgramDto.setUserName(nativeAd.getWx_mini_user());
                                    responseMiniProgramDto.setPath(nativeAd.getWx_mini_path());
                                }
                                tagResponseDto.setMiniProgram(responseMiniProgramDto);
                                break;
                        }
                    }

                    // 竞胜竞败通知链接
                    if (null != nativeAd.getWurls() && !nativeAd.getWurls().isEmpty()) {
                        tagResponseDto.setWinNoticeUrls(nativeAd.getWurls());
                    }
                    if (null != nativeAd.getLurls() && !nativeAd.getLurls().isEmpty()) {
                        tagResponseDto.setFailNoticeUrls(nativeAd.getLurls());
                    }

                    // 展现监测链接
                    if (null != nativeAd.getImp_urls() && !nativeAd.getImp_urls().isEmpty()) {
                        tracks.add(new ResponseTrackDto(EventType.EXPOSURE.getType(), nativeAd.getImp_urls()));
                    }

                    // 点击监测链接
                    if (null != nativeAd.getClick_urls() && !nativeAd.getClick_urls().isEmpty()) {
                        tracks.add(new ResponseTrackDto(EventType.CLICK.getType(), nativeAd.getClick_urls()));
                    }

                    // 下载相关监测链接
                    if (null != nativeAd.getDownload_urls() && !nativeAd.getDownload_urls().isEmpty()) {
                        tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_BEGIN.getType(), nativeAd.getDownload_urls()));
                    }
                    if (null != nativeAd.getDownloaded_urls() && !nativeAd.getDownloaded_urls().isEmpty()) {
                        tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_COMPLETED.getType(), nativeAd.getDownloaded_urls()));
                    }

                    // 安装相关监测链接
                    if (null != nativeAd.getInstalled_urls() && !nativeAd.getInstalled_urls().isEmpty()) {
                        tracks.add(new ResponseTrackDto(EventType.INSTALL_COMPLETED.getType(), nativeAd.getInstalled_urls()));
                    }
                    if (null != nativeAd.getOpen_urls() && !nativeAd.getOpen_urls().isEmpty()) {
                        tracks.add(new ResponseTrackDto(EventType.INSTALLED_OPEN.getType(), nativeAd.getOpen_urls()));
                    }

                    // 深度链接相关监测链接
                    if (null != nativeAd.getDpsucc_urls() && !nativeAd.getDpsucc_urls().isEmpty()) {
                        tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_SUCCESS.getType(), nativeAd.getDpsucc_urls()));
                    }
                    if (null != nativeAd.getDpfail_urls() && !nativeAd.getDpfail_urls().isEmpty()) {
                        tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_FAIL.getType(), nativeAd.getDpfail_urls()));
                    }
                    if (null != nativeAd.getDpinst_urls() && !nativeAd.getDpinst_urls().isEmpty()) {
                        tracks.add(new ResponseTrackDto(EventType.DEEPLINK_START.getType(), nativeAd.getDpinst_urls()));
                    }
                    if (null != nativeAd.getDpuninst_urls() && !nativeAd.getDpuninst_urls().isEmpty()) {
                        tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_FAIL.getType(), nativeAd.getDpuninst_urls()));
                    }
                }
            }


            // 宏替换
            if (null != tagResponseDto.getTracks()) {
                tagResponseDto.getTracks().forEach(track -> {
                    List<String> urls = track.getTrackUrls();
                    urls = replaceMacro("__PRICE__", urls, tagResponseDto.getPrice().toString());
                    urls = replaceMacro("__TS__", urls, MacroType.TIME.getCode());
                    urls = replaceMacro("__SECONDS__", urls, MacroType.TIME_SECONDS.getCode());
                    urls = replaceMacro("__LOSS_PR__", urls, MacroType.LOSS_RP.getCode());
                    track.setTrackUrls(urls);
                });
            }
            responseDto.getTags().add(tagResponseDto);
        });
        return responseDto;
    }


    private MadBoxiRequestGeo convertGeo(RequestGeoDto info) {
        MadBoxiRequestGeo geo = new MadBoxiRequestGeo();
        if (null != info) {
            if (null != info.getLatitude()) {
                geo.setLatitude(info.getLatitude().floatValue());
            }
            if (null != info.getLongitude()) {
                geo.setLongitude(info.getLongitude().floatValue());
            }
        }
        return geo;
    }


    private List<MadBoxiRequestImp> convertSlot(RtbRequestDto requestDto, Double price) {
        MadBoxiRequestImp request = new MadBoxiRequestImp();
        request.setId(requestDto.getReqId());
        request.setBidfloor(price);
        request.setSecure(requestDto.getTag().getNeedHttps() != null && requestDto.getTag().getNeedHttps() ? 1 : 0);
        return Collections.singletonList(request);
    }

    private MadBoxiRequestDevice convertDevice(RequestDeviceDto deviceInfo, RequestNetworkDto networkInfo, RequestGeoDto geo) {
        MadBoxiRequestDevice request = new MadBoxiRequestDevice();
        // 操作系统相关
        if (null != deviceInfo.getOsType()) {
            switch (deviceInfo.getOsType()) {
                case ANDROID:
                    request.setOs("1");
                    break;
                case IOS:
                    request.setOs("2");
                    break;
                default:
                    request.setOs("3"); // OTT
                    break;
            }
        }
        request.setOs_version(null != deviceInfo.getOsVersion() ? deviceInfo.getOsVersion() : "");
        request.setUa(null != deviceInfo.getUserAgent() ? deviceInfo.getUserAgent() : "");
        request.setIp(null != networkInfo && null != networkInfo.getIp() ? networkInfo.getIp() : "");
        request.setMake(null != deviceInfo.getVendor() ? deviceInfo.getVendor() : "");
        request.setModel(null != deviceInfo.getModel() ? deviceInfo.getModel() : "");

        // 屏幕尺寸
        if (null != deviceInfo.getWidth()) {
            request.setWidth(deviceInfo.getWidth());
        }
        if (null != deviceInfo.getHeight()) {
            request.setHeight(deviceInfo.getHeight());
        }

        // 设备标识符
        request.setImei(null != deviceInfo.getImei() ? deviceInfo.getImei() : "");
        request.setImeimd5(null != deviceInfo.getImeiMd5() ? deviceInfo.getImeiMd5().toLowerCase() : "");
        request.setOaid(null != deviceInfo.getOaid() ? deviceInfo.getOaid() : "");
        request.setOaid_md5(null != deviceInfo.getOaidMd5() ? deviceInfo.getOaidMd5().toLowerCase() : "");
        request.setAndroidId(null != deviceInfo.getAndroidId() ? deviceInfo.getAndroidId() : "");
        request.setAndroidid_md5(null != deviceInfo.getAndroidIdMd5() ? deviceInfo.getAndroidIdMd5().toLowerCase() : "");
        request.setMac(null != networkInfo && null != networkInfo.getMac() ? networkInfo.getMac() : "");
        request.setMacmd5(null != networkInfo && null != networkInfo.getMacMd5() ? networkInfo.getMacMd5().toLowerCase() : "");
        request.setIdfa(null != deviceInfo.getIdfa() ? deviceInfo.getIdfa() : "");
        request.setIdfa_md5(null != deviceInfo.getIdfaMd5() ? deviceInfo.getIdfaMd5().toLowerCase() : "");
        request.setOpenudid(null != deviceInfo.getOpenUdId() ? deviceInfo.getOpenUdId() : "");
        // CAID信息
        List<MadBoxiRequestCaid> caids = new ArrayList<>();
        if (null != deviceInfo.getCaids() && !deviceInfo.getCaids().isEmpty()) {
            deviceInfo.getCaids().forEach(item -> {
                if (StringUtils.isNotEmpty(item.getCaid()) && StringUtils.isNotEmpty(item.getVersion())) {
                    caids.add(new MadBoxiRequestCaid(item.getCaid(), item.getVersion()));
                    request.setCaid(item.getCaid());
                    request.setCaidVer(item.getVersion());
                }
            });
        }
        request.setCaids(caids);
        // 网络连接类型
        if (null != networkInfo && null != networkInfo.getConnectType()) {
            switch (networkInfo.getConnectType()) {
                case WIFI:
                    request.setConnectiontype(1);
                    break;
                case NETWORK_2G:
                    request.setConnectiontype(2);
                    break;
                case NETWORK_3G:
                    request.setConnectiontype(3);
                    break;
                case NETWORK_4G:
                    request.setConnectiontype(4);
                    break;
                case NETWORK_5G:
                    request.setConnectiontype(5);
                    break;
                default:
                    request.setConnectiontype(0);
                    break;
            }
        }

        // 运营商
        if (null != networkInfo && null != networkInfo.getCarrierType()) {
            switch (networkInfo.getCarrierType()) {
                case CM:
                    request.setCarrier(1);
                    break;
                case CU:
                    request.setCarrier(2);
                    break;
                case CT:
                    request.setCarrier(3);
                    break;
                default:
                    request.setCarrier(0);
                    break;
            }
        }

        // 其他设备信息
        if (null != deviceInfo.getPpi()) {
            request.setPpi(deviceInfo.getPpi());
        }
        request.setSsid(null != networkInfo && null != networkInfo.getSsid() ? networkInfo.getSsid() : "");
        request.setRom_version(null != deviceInfo.getSysUiVersion() ? deviceInfo.getSysUiVersion() : "");
        request.setHms(null != deviceInfo.getHmsVersion() ? deviceInfo.getHmsVersion() : "");
        request.setAg(null != deviceInfo.getAppStoreVersion() ? deviceInfo.getAppStoreVersion() : "");
        request.setBoot_mark(null != deviceInfo.getBootMark() ? deviceInfo.getBootMark() : "");
        request.setUpdate_mark(null != deviceInfo.getUpdateMark() ? deviceInfo.getUpdateMark() : "");
        request.setPaid(null != deviceInfo.getPaid() ? deviceInfo.getPaid() : "");
        request.setAaid(null != deviceInfo.getAaid() ? deviceInfo.getAaid() : "");
        request.setHardware_machine(null != deviceInfo.getHardwareMachine() ? deviceInfo.getHardwareMachine() : "");

        // 时间相关
        Long startTime = TimeUtils.convertMilliSecond(deviceInfo.getSysStartTime());
        if (null != startTime) {
            request.setStartup_time(startTime.toString());
        }
        request.setMb_time(StringUtils.isNotEmpty(deviceInfo.getSysUpdateTime()) ? deviceInfo.getSysUpdateTime() : "");
        request.setCountry_code(null != deviceInfo.getCountry() ? deviceInfo.getCountry() : "");
        request.setLanguage(null != deviceInfo.getLanguage() ? deviceInfo.getLanguage() : "");

        // 内存和磁盘
        if (null != deviceInfo.getDeviceMemory()) {
            request.setMem_total(deviceInfo.getDeviceMemory().intValue());
        }
        if (null != deviceInfo.getDeviceHardDisk()) {
            request.setDisk_total(deviceInfo.getDeviceHardDisk().intValue());
        }

        request.setLocal_tz_name(null != deviceInfo.getTimeZone() ? deviceInfo.getTimeZone() : "");
        request.setHardware_model(null != deviceInfo.getModel() ? deviceInfo.getModel() : "");
        request.setIos_os_version(null != deviceInfo.getOsVersion() ? deviceInfo.getOsVersion() : "");

        // 设备名称MD5
        if (StringUtils.isNotEmpty(deviceInfo.getDeviceName())) {
            request.setPhone_name(Md5.md5(deviceInfo.getDeviceName()).toLowerCase());
        } else if (StringUtils.isNotEmpty(deviceInfo.getDeviceNameMd5())) {
            request.setPhone_name(deviceInfo.getDeviceNameMd5().toLowerCase());
        } else {
            request.setPhone_name("");
        }
        if (deviceInfo.getCpuNum() != null && deviceInfo.getCpuNum() > 0) {
            request.setCpu_num(deviceInfo.getCpuNum().toString());
        }
        if (deviceInfo.getInstalledAppInfo() != null) {
            final String[] appStr = {""};
            deviceInfo.getInstalledAppInfo().forEach(item -> {
                if (StringUtils.isNotEmpty(item.getPackageName())) {
                    appStr[0] += item.getPackageName() + ",";
                }
            });
            request.setApp_list(appStr[0]);
        }

        if (geo != null) {
            request.setGeo(convertGeo(geo));
        }

        // 设备初始化时间
        request.setDevice_filetime(null != deviceInfo.getSysInitTime() ? deviceInfo.getSysInitTime() : "");
        return request;
    }
}
