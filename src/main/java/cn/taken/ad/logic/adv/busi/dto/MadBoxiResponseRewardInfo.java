package cn.taken.ad.logic.adv.busi.dto;

import java.io.Serializable;

/**
 * MADBOXI激励视频信息
 */
public class MadBoxiResponseRewardInfo implements Serializable {
    private static final long serialVersionUID = -831049895855023138L;
    
    /**
     * 跳过显示时间（秒）
     */
    private Integer skipShowTime;
    
    /**
     * 奖励时间（秒）
     */
    private Integer rewardTime;
    
    /**
     * 是否显示落地页 1:显示 0:不显示
     */
    private Integer showLandingPage;

    public Integer getSkipShowTime() {
        return skipShowTime;
    }

    public void setSkipShowTime(Integer skipShowTime) {
        this.skipShowTime = skipShowTime;
    }

    public Integer getRewardTime() {
        return rewardTime;
    }

    public void setRewardTime(Integer rewardTime) {
        this.rewardTime = rewardTime;
    }

    public Integer getShowLandingPage() {
        return showLandingPage;
    }

    public void setShowLandingPage(Integer showLandingPage) {
        this.showLandingPage = showLandingPage;
    }
}
