package cn.taken.ad.logic.adv.busi.dto;

import java.util.List;

public class MadBoxiResponseNative {
    private List<MadBoxiResponseAsset> assets;
    private String url;
    private String deeplinkUrl;
    private String universalLink;
    private String marketUrl;
    private String wxMiniUser;
    private String wxMiniPath;
    private List<String> impUrls;
    private List<String> clickUrls;
    private List<String> downloadUrls;
    private List<String> downloadedUrls;
    private List<String> installedUrls;
    private List<String> openUrls;
    private List<String> dpsuccUrls;
    private List<String> dpfailUrls;
    private List<String> dpinstUrls;
    private List<String> dpuninstUrls;
    private List<String> wurls;
    private List<String> lurls;
    private String appName;
    private String appPackage;
    private String appVersion;
    private String appIcon;
    private Long appSize;
    private String permissionUrl;
    private String privacyUrl;
    private String developerName;
    private String introductionUrl;
    private String downloadUrl;
    private String clickAreaReportUrl;

    public List<MadBoxiResponseAsset> getAssets() {
        return assets;
    }

    public void setAssets(List<MadBoxiResponseAsset> assets) {
        this.assets = assets;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getDeeplinkUrl() {
        return deeplinkUrl;
    }

    public void setDeeplinkUrl(String deeplinkUrl) {
        this.deeplinkUrl = deeplinkUrl;
    }

    public String getUniversalLink() {
        return universalLink;
    }

    public void setUniversalLink(String universalLink) {
        this.universalLink = universalLink;
    }

    public String getMarketUrl() {
        return marketUrl;
    }

    public void setMarketUrl(String marketUrl) {
        this.marketUrl = marketUrl;
    }

    public String getWxMiniUser() {
        return wxMiniUser;
    }

    public void setWxMiniUser(String wxMiniUser) {
        this.wxMiniUser = wxMiniUser;
    }

    public String getWxMiniPath() {
        return wxMiniPath;
    }

    public void setWxMiniPath(String wxMiniPath) {
        this.wxMiniPath = wxMiniPath;
    }

    public List<String> getImpUrls() {
        return impUrls;
    }

    public void setImpUrls(List<String> impUrls) {
        this.impUrls = impUrls;
    }

    public List<String> getClickUrls() {
        return clickUrls;
    }

    public void setClickUrls(List<String> clickUrls) {
        this.clickUrls = clickUrls;
    }

    public List<String> getDownloadUrls() {
        return downloadUrls;
    }

    public void setDownloadUrls(List<String> downloadUrls) {
        this.downloadUrls = downloadUrls;
    }

    public List<String> getDownloadedUrls() {
        return downloadedUrls;
    }

    public void setDownloadedUrls(List<String> downloadedUrls) {
        this.downloadedUrls = downloadedUrls;
    }

    public List<String> getInstalledUrls() {
        return installedUrls;
    }

    public void setInstalledUrls(List<String> installedUrls) {
        this.installedUrls = installedUrls;
    }

    public List<String> getOpenUrls() {
        return openUrls;
    }

    public void setOpenUrls(List<String> openUrls) {
        this.openUrls = openUrls;
    }

    public List<String> getDpsuccUrls() {
        return dpsuccUrls;
    }

    public void setDpsuccUrls(List<String> dpsuccUrls) {
        this.dpsuccUrls = dpsuccUrls;
    }

    public List<String> getDpfailUrls() {
        return dpfailUrls;
    }

    public void setDpfailUrls(List<String> dpfailUrls) {
        this.dpfailUrls = dpfailUrls;
    }

    public List<String> getDpinstUrls() {
        return dpinstUrls;
    }

    public void setDpinstUrls(List<String> dpinstUrls) {
        this.dpinstUrls = dpinstUrls;
    }

    public List<String> getDpuninstUrls() {
        return dpuninstUrls;
    }

    public void setDpuninstUrls(List<String> dpuninstUrls) {
        this.dpuninstUrls = dpuninstUrls;
    }

    public List<String> getWurls() {
        return wurls;
    }

    public void setWurls(List<String> wurls) {
        this.wurls = wurls;
    }

    public List<String> getLurls() {
        return lurls;
    }

    public void setLurls(List<String> lurls) {
        this.lurls = lurls;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppPackage() {
        return appPackage;
    }

    public void setAppPackage(String appPackage) {
        this.appPackage = appPackage;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public String getAppIcon() {
        return appIcon;
    }

    public void setAppIcon(String appIcon) {
        this.appIcon = appIcon;
    }

    public Long getAppSize() {
        return appSize;
    }

    public void setAppSize(Long appSize) {
        this.appSize = appSize;
    }

    public String getPermissionUrl() {
        return permissionUrl;
    }

    public void setPermissionUrl(String permissionUrl) {
        this.permissionUrl = permissionUrl;
    }

    public String getPrivacyUrl() {
        return privacyUrl;
    }

    public void setPrivacyUrl(String privacyUrl) {
        this.privacyUrl = privacyUrl;
    }

    public String getDeveloperName() {
        return developerName;
    }

    public void setDeveloperName(String developerName) {
        this.developerName = developerName;
    }

    public String getIntroductionUrl() {
        return introductionUrl;
    }

    public void setIntroductionUrl(String introductionUrl) {
        this.introductionUrl = introductionUrl;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public String getClickAreaReportUrl() {
        return clickAreaReportUrl;
    }

    public void setClickAreaReportUrl(String clickAreaReportUrl) {
        this.clickAreaReportUrl = clickAreaReportUrl;
    }
}
