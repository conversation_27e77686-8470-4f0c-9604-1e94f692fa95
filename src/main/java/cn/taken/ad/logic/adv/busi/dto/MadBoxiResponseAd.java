package cn.taken.ad.logic.adv.busi.dto;

import java.io.Serializable;

/**
 * MADBOXI广告响应信息
 */
public class MadBoxiResponseAd implements Serializable {
    private static final long serialVersionUID = -831049895855023134L;

    /**
     * 广告ID
     */
    private String id;

    private Integer interact_type;
    /**
     * 出价（分）
     */
    private Integer price;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getInteract_type() {
        return interact_type;
    }

    public void setInteract_type(Integer interact_type) {
        this.interact_type = interact_type;
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }
}
