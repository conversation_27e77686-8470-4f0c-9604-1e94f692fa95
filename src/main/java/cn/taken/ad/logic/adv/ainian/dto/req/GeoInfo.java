package cn.taken.ad.logic.adv.ainian.dto.req;

public class GeoInfo {
    private String userAgent; //设备userAgent
    private String ip; //Ipv4 、 Ipv6 地址必填一个
    private String ipv6; //Ipv6 地址必填一个
    private Float latitude; //纬度(-90~90)
    private Float longitude; //经度(-180~180)
    private Integer coordinateType; //坐标类型：1 全球卫星定位系统坐标系;2 国家测绘局坐标系;3 百度坐标系;
    private String mac; //MAC 地址，⼤写明⽂，保留冒号
    private String macMd5; //MAC 地址，保留冒号的 md5 值
    private String imsi; //国际移动用户识别码
    private String ssid; //无线网络的名称

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getIpv6() {
        return ipv6;
    }

    public void setIpv6(String ipv6) {
        this.ipv6 = ipv6;
    }

    public Float getLatitude() {
        return latitude;
    }

    public void setLatitude(Float latitude) {
        this.latitude = latitude;
    }

    public Float getLongitude() {
        return longitude;
    }

    public void setLongitude(Float longitude) {
        this.longitude = longitude;
    }

    public Integer getCoordinateType() {
        return coordinateType;
    }

    public void setCoordinateType(Integer coordinateType) {
        this.coordinateType = coordinateType;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getMacMd5() {
        return macMd5;
    }

    public void setMacMd5(String macMd5) {
        this.macMd5 = macMd5;
    }

    public String getImsi() {
        return imsi;
    }

    public void setImsi(String imsi) {
        this.imsi = imsi;
    }

    public String getSsid() {
        return ssid;
    }

    public void setSsid(String ssid) {
        this.ssid = ssid;
    }
}
