package cn.taken.ad.logic.media.yinghuochong.dto.request;

import java.util.List;

public class AdRequest {
    private String reqId;
    private String ip;
    private String ipv6;
    private List<AdSlot> adSlots;
    private App app;
    private Device device;
    private UserLocation userLocation;
    private Integer tmax;
    private Integer reportPriceType;

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getIpv6() {
        return ipv6;
    }

    public void setIpv6(String ipv6) {
        this.ipv6 = ipv6;
    }

    public List<AdSlot> getAdSlots() {
        return adSlots;
    }

    public void setAdSlots(List<AdSlot> adSlots) {
        this.adSlots = adSlots;
    }

    public App getApp() {
        return app;
    }

    public void setApp(App app) {
        this.app = app;
    }

    public Device getDevice() {
        return device;
    }

    public void setDevice(Device device) {
        this.device = device;
    }

    public UserLocation getUserLocation() {
        return userLocation;
    }

    public void setUserLocation(UserLocation userLocation) {
        this.userLocation = userLocation;
    }

    public Integer getTmax() {
        return tmax;
    }

    public void setTmax(Integer tmax) {
        this.tmax = tmax;
    }

    public Integer getReportPriceType() {
        return reportPriceType;
    }

    public void setReportPriceType(Integer reportPriceType) {
        this.reportPriceType = reportPriceType;
    }
}
